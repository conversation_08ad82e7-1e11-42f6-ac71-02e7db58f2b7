name: CI/CD DSWC

on:
  push:
    branches:
      - develop
    paths-ignore:
      - '**.md'
  pull_request:
    branches:
      - develop
    paths-ignore:
      - '**.md'
  workflow_dispatch:

env:
  ECR_REGISTRY: ${{ vars.ECR_REGISTRY }}
  KUBE_CLUSTER: remote-cluster
  KUBE_USERNAME: deploy-user
  KUBE_CONTEXT: deployment-context
  KUBE_NAMESPACE: onepage

jobs:
  test:
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Run tests
        run: yarn test

  build-and-push:
    if: github.event_name == 'push'
    runs-on: ubuntu-latest
    environment:
      name: ${{ github.ref_name }}
    env:
      IMAGE_TAG: ${{ vars.ENVIRONMENT }}-${{ github.sha }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}

      - name: Log in to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build Docker image
        run: |
          docker build -t ${ECR_REGISTRY}:${IMAGE_TAG} .

      - name: Push Docker image to ECR
        run: |
          docker push ${ECR_REGISTRY}:${IMAGE_TAG}

  deploy:
    if: github.event_name == 'push'
    needs: build-and-push
    runs-on: ubuntu-latest
    strategy:
      matrix:
        chart: [onepage-backend, onepage-worker]
    environment:
      name: ${{ github.ref_name }}
    env:
      ENVIRONMENT: ${{ vars.ENVIRONMENT }}
      KUBE_API_SERVER: ${{ vars.KUBE_API_SERVER }}
      KUBE_TOKEN: ${{ secrets.KUBE_TOKEN }}
      IMAGE_TAG: ${{ vars.ENVIRONMENT }}-${{ github.sha }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up kubectl
        run: |
          kubectl config set-cluster ${KUBE_CLUSTER} --server=${KUBE_API_SERVER} --insecure-skip-tls-verify=true
          kubectl config set-credentials ${KUBE_USERNAME} --token=${KUBE_TOKEN}
          kubectl config set-context ${KUBE_CONTEXT} --cluster=${KUBE_CLUSTER} --user=${KUBE_USERNAME}
          kubectl config use-context ${KUBE_CONTEXT}

      - name: Install Helm
        uses: azure/setup-helm@v4

      - name: Install yq
        run: |
          curl -L https://github.com/mikefarah/yq/releases/download/v4.45.4/yq_linux_amd64 -o yq
          chmod +x yq
          mv yq /usr/local/bin/yq

      - name: Inject Env
        env: {}
        # use ENV_ as prefix to isolate other credentials
        run: |
          for ENV in $(printenv | grep -E '^ENV_.+')
          do
            ENV_KEY=$(echo $ENV | awk '{split($0, arr, "="); print substr(arr[1], 5)}')
            ENV_VALUE=$(echo $ENV | awk '{split($0, arr, "="); print arr[2]}')
            YQ_ENV_KEY=${ENV_KEY} YQ_ENV_VALUE=${ENV_VALUE} yq -i '.env += {strenv(YQ_ENV_KEY): strenv(YQ_ENV_VALUE)}' ./helm/${{ matrix.chart }}/values-${ENVIRONMENT}.yaml
          done

      - name: Inject Secret Env
        env:
          # use SECRET_ENV_ as prefix to isolate other credentials
          SECRET_ENV_APP_PORT: ${{ secrets.APP_PORT }}
          SECRET_APP_CLIENT_URL: ${{ secrets.APP_CLIENT_URL }}
          SECRET_ENV_APP_CORS: ${{ secrets.APP_CORS }}
          # SECRET_ENV_APP_CORS_ALLOW_PATHS: ${{ secrets.APP_CORS_ALLOW_PATHS }}
          SECRET_ENV_DATABASE_LOG: ${{ secrets.DATABASE_LOG }}
          SECRET_ENV_DATABASE_NAME: ${{ secrets.DATABASE_NAME }}
          SECRET_ENV_DATABASE_PASSWORD: ${{ secrets.DATABASE_PASSWORD }}
          SECRET_ENV_DATABASE_PORT: ${{ secrets.DATABASE_PORT }}
          SECRET_ENV_DATABASE_URL: ${{ secrets.DATABASE_URL }}
          SECRET_ENV_DATABASE_USERNAME: ${{ secrets.DATABASE_USERNAME }}
          SECRET_ENV_DX_API_TOKEN: ${{ secrets.DX_API_TOKEN }}
          SECRET_ENV_DX_BASE_URL: ${{ secrets.DX_BASE_URL }}
          SECRET_ENV_EMAIL_FROM: ${{ secrets.EMAIL_FROM }}
          SECRET_ENV_EMAIL_HOST: ${{ secrets.EMAIL_HOST }}
          SECRET_ENV_EMAIL_PASSWORD: ${{ secrets.EMAIL_PASSWORD }}
          SECRET_ENV_EMAIL_PORT: ${{ secrets.EMAIL_PORT }}
          SECRET_ENV_EMAIL_SECURE: ${{ secrets.EMAIL_SECURE }}
          SECRET_ENV_EMAIL_USER: ${{ secrets.EMAIL_USER }}
          SECRET_ENV_ENABLE_BULL_BOARD: ${{ secrets.ENABLE_BULL_BOARD }}
          SECRET_ENV_ENABLE_CACHE: ${{ secrets.ENABLE_CACHE }}
          SECRET_ENV_ENABLE_DB_LOG: ${{ secrets.ENABLE_DB_LOG }}
          SECRET_ENV_ENABLE_JSON_LOG: ${{ secrets.ENABLE_JSON_LOG }}
          SECRET_ENV_ENABLE_SWAGGER: ${{ secrets.ENABLE_SWAGGER }}
          SECRET_ENV_JWT_EXPIRE_IN_SECOND: ${{ secrets.JWT_EXPIRE_IN_SECOND }}
          SECRET_ENV_JWT_SALT: ${{ secrets.JWT_SALT }}
          SECRET_ENV_MINIO_ACCESS_KEY: ${{ secrets.MINIO_ACCESS_KEY }}
          SECRET_ENV_MINIO_BUCKET: ${{ secrets.MINIO_BUCKET }}
          SECRET_ENV_MINIO_ENDPOINT: ${{ secrets.MINIO_ENDPOINT }}
          SECRET_ENV_MINIO_PORT: ${{ secrets.MINIO_PORT }}
          SECRET_ENV_MINIO_SECRET_KEY: ${{ secrets.MINIO_SECRET_KEY }}
          SECRET_ENV_MINIO_USE_SSL: ${{ secrets.MINIO_USE_SSL }}
          SECRET_ENV_NODE_ENV: ${{ secrets.NODE_ENV }}
          SECRET_ENV_REDIS_PASSWORD: ${{ secrets.REDIS_PASSWORD }}
          SECRET_ENV_REDIS_URL: ${{ secrets.REDIS_URL }}
        run: |
          for SECRET_ENV in $(printenv | grep -E '^SECRET_ENV_.+')
          do
            SECRET_KEY=$(echo $SECRET_ENV | awk '{split($0, arr, "="); print substr(arr[1], 12)}')
            SECRET_VALUE=$(echo $SECRET_ENV | awk '{split($0, arr, "="); print arr[2]}')
            YQ_SECRET_KEY=${SECRET_KEY} YQ_SECRET_VALUE=${SECRET_VALUE} yq -i '.secretEnv += {strenv(YQ_SECRET_KEY): strenv(YQ_SECRET_VALUE)}' ./helm/${{ matrix.chart }}/values-${ENVIRONMENT}.yaml
          done

      - name: Deploy with Helm
        run: |
          helm upgrade --install ${{ matrix.chart }} --namespace=${KUBE_NAMESPACE} ./helm/${{ matrix.chart }} \
            -f ./helm/${{ matrix.chart }}/values.yaml \
            -f ./helm/${{ matrix.chart }}/values-${ENVIRONMENT}.yaml \
            --set image.repository=${ECR_REGISTRY} \
            --set image.tag=${IMAGE_TAG} \
            --wait --timeout 10m --debug
