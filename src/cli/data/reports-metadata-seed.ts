import {
  ApproveStatus,
  DocumentActionType,
} from '@domain/v1/documents/documents.v1.constant';

export interface ReportSeedData {
  name: string;
  status: string;
  actionType: string;
  backgroundColor: string;
  data: any;
  responsibilityAreaId: number;
  layoutId: number;
  disasterIds: number[];
  createdByUserId: number;
  reviewedByUserId?: number;
  latestRequestedByUserId?: number;
  latestEditedByUserId?: number;
  createdAt: Date;
  reviewedAt?: Date;
  latestRequestedAt?: Date;
  latestEditedAt?: Date;
  thumbnailPath?: string;
  shouldSoftDelete?: boolean;
  deletedByUserId?: number;
  deletedAt?: Date;
}
const daysAgo = (days: number): Date => {
  const date = new Date();
  date.setDate(date.getDate() - days);
  return date;
};
const hoursAgo = (hours: number): Date => {
  const date = new Date();
  date.setHours(date.getHours() - hours);
  return date;
};
const minutesAgo = (minutes: number): Date => {
  const date = new Date();
  date.setMinutes(date.getMinutes() - minutes);
  return date;
};
const daysFromNow = (days: number): Date => {
  const date = new Date();
  date.setDate(date.getDate() + days);
  return date;
};
const generateReportContent = (type: string, location?: string): any => {
  const baseContent = {
    version: '1.0',
    metadata: {
      generatedAt: new Date().toISOString(),
      reportType: type,
      location: location || 'Thailand',
    },
  };
  switch (type) {
    case 'flood':
      return {
        ...baseContent,
        content: 'Comprehensive flood response and monitoring procedures',
        sections: [
          'Risk Assessment',
          'Evacuation Plans',
          'Resource Allocation',
          'Recovery Procedures',
        ],
        riskLevel: 'High',
        affectedAreas: ['Residential', 'Commercial', 'Agricultural'],
      };
    case 'earthquake':
      return {
        ...baseContent,
        content: 'Earthquake preparedness and emergency response guidelines',
        sections: [
          'Seismic Monitoring',
          'Building Safety',
          'Emergency Response',
          'Public Education',
        ],
        magnitude: '6.0+',
        preparednessLevel: 'Advanced',
      };
    case 'fire':
      return {
        ...baseContent,
        content: 'Fire safety protocols and emergency response procedures',
        sections: ['Prevention', 'Detection', 'Suppression', 'Evacuation'],
        fireType: 'Wildfire/Urban',
        responseTime: '< 10 minutes',
      };
    default:
      return {
        ...baseContent,
        content: `${type} disaster management and response procedures`,
        sections: ['Assessment', 'Response', 'Recovery', 'Prevention'],
      };
  }
};
export const reportsMetadataSeed: ReportSeedData[] = [
  {
    name: 'Bangkok Metropolitan Flood Response Plan 2024',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Create,
    backgroundColor: '#3B82F6',
    data: generateReportContent('flood', 'Bangkok'),
    responsibilityAreaId: 1,
    layoutId: 1,
    disasterIds: [7],
    createdByUserId: 1,
    reviewedByUserId: 2,
    createdAt: daysAgo(45),
    reviewedAt: daysAgo(40),
    thumbnailPath: 'thumbnails/reports/flood-bangkok-low.jpg',
  },
  {
    name: 'Northern Thailand Earthquake Preparedness Protocol',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Create,
    backgroundColor: '#EF4444',
    data: generateReportContent('earthquake', 'Chiang Mai'),
    responsibilityAreaId: 2,
    layoutId: 17,
    disasterIds: [1],
    createdByUserId: 2,
    reviewedByUserId: 1,
    latestEditedByUserId: 3,
    createdAt: daysAgo(60),
    reviewedAt: daysAgo(55),
    latestEditedAt: daysAgo(50),
    thumbnailPath: 'thumbnails/reports/earthquake-cm-low.jpg',
  },
  {
    name: 'Phuket Tourism Area Fire Safety Guidelines',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Edit,
    backgroundColor: '#F59E0B',
    data: generateReportContent('fire', 'Phuket'),
    responsibilityAreaId: 3,
    layoutId: 2,
    disasterIds: [5],
    createdByUserId: 3,
    reviewedByUserId: 1,
    latestRequestedByUserId: 2,
    latestEditedByUserId: 3,
    createdAt: daysAgo(30),
    reviewedAt: daysAgo(25),
    latestRequestedAt: daysAgo(28),
    latestEditedAt: daysAgo(20),
    thumbnailPath: 'thumbnails/reports/fire-phuket-low.jpg',
  },
  {
    name: 'Bangkok Air Quality Monitoring System',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Create,
    backgroundColor: '#10B981',
    data: generateReportContent('air-quality', 'Bangkok'),
    responsibilityAreaId: 1,
    layoutId: 18,
    disasterIds: [6],
    createdByUserId: 1,
    reviewedByUserId: 3,
    createdAt: daysAgo(75),
    reviewedAt: daysAgo(70),
    thumbnailPath: 'thumbnails/reports/air-quality-low.jpg',
  },
  {
    name: 'National Dam Water Level Alert Network',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Create,
    backgroundColor: '#6366F1',
    data: generateReportContent('dam-monitoring', 'National'),
    responsibilityAreaId: 2,
    layoutId: 3,
    disasterIds: [4],
    createdByUserId: 2,
    reviewedByUserId: 1,
    createdAt: daysAgo(120),
    reviewedAt: daysAgo(115),
    thumbnailPath: 'thumbnails/reports/dam-alert-low.jpg',
  },
  {
    name: 'Coastal Tsunami Early Warning System',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Create,
    backgroundColor: '#8B5CF6',
    data: generateReportContent('tsunami', 'Southern Coast'),
    responsibilityAreaId: 3,
    layoutId: 19,
    disasterIds: [8],
    createdByUserId: 3,
    reviewedByUserId: 2,
    createdAt: daysAgo(90),
    reviewedAt: daysAgo(85),
    thumbnailPath: 'thumbnails/reports/tsunami-warning-low.jpg',
  },
  {
    name: 'Advanced Weather Monitoring Network Expansion',
    status: ApproveStatus.WaitForApprove,
    actionType: DocumentActionType.Create,
    backgroundColor: '#F97316',
    data: generateReportContent('weather-monitoring', 'National'),
    responsibilityAreaId: 1,
    layoutId: 4,
    disasterIds: [3],
    createdByUserId: 1,
    latestRequestedByUserId: 2,
    createdAt: daysAgo(8),
    latestRequestedAt: daysAgo(5),
    thumbnailPath: 'thumbnails/reports/weather-network-low.jpg',
  },
  {
    name: 'Urban Heat Island Mitigation Strategy',
    status: ApproveStatus.WaitForApprove,
    actionType: DocumentActionType.Edit,
    backgroundColor: '#DC2626',
    data: generateReportContent('heat-wave', 'Bangkok'),
    responsibilityAreaId: 1,
    layoutId: 20,
    disasterIds: [2],
    createdByUserId: 2,
    latestEditedByUserId: 1,
    latestRequestedByUserId: 3,
    createdAt: daysAgo(12),
    latestEditedAt: daysAgo(6),
    latestRequestedAt: daysAgo(4),
    thumbnailPath: 'thumbnails/reports/heatwave-low.jpg',
  },
  {
    name: 'Integrated River Basin Management System',
    status: ApproveStatus.WaitForApprove,
    actionType: DocumentActionType.Create,
    backgroundColor: '#0891B2',
    data: generateReportContent('river-monitoring', 'Central Thailand'),
    responsibilityAreaId: 2,
    layoutId: 21,
    disasterIds: [9, 4],
    createdByUserId: 3,
    latestRequestedByUserId: 1,
    createdAt: daysAgo(15),
    latestRequestedAt: daysAgo(10),
    thumbnailPath: 'thumbnails/reports/river-management-low.jpg',
  },
  {
    name: 'Community-Based Disaster Preparedness Program',
    status: ApproveStatus.WaitForApprove,
    actionType: DocumentActionType.Create,
    backgroundColor: '#059669',
    data: generateReportContent('community-preparedness', 'Rural Areas'),
    responsibilityAreaId: 3,
    layoutId: 5,
    disasterIds: [1, 3, 5],
    createdByUserId: 1,
    latestRequestedByUserId: 2,
    createdAt: daysAgo(20),
    latestRequestedAt: daysAgo(15),
    thumbnailPath: 'thumbnails/reports/community-prep-low.jpg',
  },
  {
    name: 'Outdated Emergency Communication Protocol',
    status: ApproveStatus.Rejected,
    actionType: DocumentActionType.Edit,
    backgroundColor: '#EC4899',
    data: generateReportContent('communication', 'National'),
    responsibilityAreaId: 2,
    layoutId: 22,
    disasterIds: [1],
    createdByUserId: 2,
    reviewedByUserId: 3,
    createdAt: daysAgo(25),
    reviewedAt: daysAgo(20),
  },
  {
    name: 'Smart City Disaster Management Framework',
    status: ApproveStatus.Draft,
    actionType: DocumentActionType.Draft,
    backgroundColor: '#6B7280',
    data: generateReportContent('smart-city', 'Bangkok'),
    responsibilityAreaId: 1,
    layoutId: 6,
    disasterIds: [3, 6, 7],
    createdByUserId: 3,
    createdAt: daysAgo(5),
  },
  {
    name: 'Regional Coordination Protocol Draft',
    status: ApproveStatus.Draft,
    actionType: DocumentActionType.Draft,
    backgroundColor: '#4B5563',
    data: generateReportContent('regional-coordination', 'Multi-Province'),
    responsibilityAreaId: 2,
    layoutId: 23,
    disasterIds: [1, 2, 4],
    createdByUserId: 1,
    createdAt: daysAgo(3),
  },
  {
    name: 'Mobile Emergency Response Unit Guidelines',
    status: ApproveStatus.Draft,
    actionType: DocumentActionType.Draft,
    backgroundColor: '#374151',
    data: generateReportContent('mobile-response', 'National'),
    responsibilityAreaId: 3,
    layoutId: 7,
    disasterIds: [1, 5, 8],
    createdByUserId: 2,
    createdAt: daysAgo(1),
  },
  {
    name: 'National Multi-Hazard Disaster Response Framework',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Create,
    backgroundColor: '#1F2937',
    data: generateReportContent('multi-hazard', 'National'),
    responsibilityAreaId: 1,
    layoutId: 24,
    disasterIds: [1, 2, 3, 4, 5, 6, 7, 8, 9],
    createdByUserId: 1,
    reviewedByUserId: 2,
    latestEditedByUserId: 3,
    createdAt: daysAgo(150),
    reviewedAt: daysAgo(145),
    latestEditedAt: daysAgo(130),
    thumbnailPath: 'thumbnails/reports/comprehensive-low.jpg',
  },
  {
    name: 'Emergency Evacuation Standard Operating Procedures',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Create,
    backgroundColor: '#DC2626',
    data: generateReportContent('evacuation', 'National'),
    responsibilityAreaId: 2,
    layoutId: 8,
    disasterIds: [1, 5],
    createdByUserId: 2,
    reviewedByUserId: 1,
    createdAt: daysAgo(100),
    reviewedAt: daysAgo(95),
    thumbnailPath: 'thumbnails/reports/evacuation-low.jpg',
  },
  {
    name: 'Community Alert and Warning Network',
    status: ApproveStatus.WaitForApprove,
    actionType: DocumentActionType.Create,
    backgroundColor: '#059669',
    data: generateReportContent('alert-system', 'Regional'),
    responsibilityAreaId: 3,
    layoutId: 9,
    disasterIds: [3, 6],
    createdByUserId: 3,
    latestRequestedByUserId: 2,
    createdAt: daysAgo(18),
    latestRequestedAt: daysAgo(12),
    thumbnailPath: 'thumbnails/reports/community-alert-low.jpg',
  },
  {
    name: 'Digital Emergency Communication Platform',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Edit,
    backgroundColor: '#7C3AED',
    data: generateReportContent('digital-communication', 'National'),
    responsibilityAreaId: 1,
    layoutId: 10,
    disasterIds: [1, 3, 7],
    createdByUserId: 1,
    reviewedByUserId: 3,
    latestEditedByUserId: 2,
    createdAt: daysAgo(65),
    reviewedAt: daysAgo(60),
    latestEditedAt: daysAgo(55),
    thumbnailPath: 'thumbnails/reports/digital-comm-low.jpg',
  },
  {
    name: 'Rapid Response Team Deployment Guidelines',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Create,
    backgroundColor: '#F59E0B',
    data: generateReportContent('rapid-response', 'Multi-Regional'),
    responsibilityAreaId: 2,
    layoutId: 11,
    disasterIds: [1, 2, 5],
    createdByUserId: 3,
    reviewedByUserId: 1,
    createdAt: daysAgo(85),
    reviewedAt: daysAgo(80),
    thumbnailPath: 'thumbnails/reports/rapid-response-low.jpg',
  },
  {
    name: 'Post-Disaster Recovery and Rehabilitation Framework',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Create,
    backgroundColor: '#7C3AED',
    data: generateReportContent('recovery', 'National'),
    responsibilityAreaId: 1,
    layoutId: 12,
    disasterIds: [1, 7, 9],
    createdByUserId: 1,
    reviewedByUserId: 3,
    createdAt: daysAgo(180),
    reviewedAt: daysAgo(175),
    thumbnailPath: 'thumbnails/reports/recovery-low.jpg',
  },
  {
    name: 'Emergency Resource Allocation and Management System',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Edit,
    backgroundColor: '#0891B2',
    data: generateReportContent('resource-management', 'Regional'),
    responsibilityAreaId: 2,
    layoutId: 13,
    disasterIds: [2, 4, 6],
    createdByUserId: 2,
    reviewedByUserId: 1,
    latestEditedByUserId: 3,
    createdAt: daysAgo(220),
    reviewedAt: daysAgo(215),
    latestEditedAt: daysAgo(200),
    thumbnailPath: 'thumbnails/reports/resource-mgmt-low.jpg',
  },
  {
    name: 'Interagency Coordination Protocol for Large-Scale Disasters',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Create,
    backgroundColor: '#10B981',
    data: generateReportContent('interagency-coordination', 'National'),
    responsibilityAreaId: 3,
    layoutId: 14,
    disasterIds: [1, 3, 5, 8],
    createdByUserId: 3,
    reviewedByUserId: 2,
    createdAt: daysAgo(160),
    reviewedAt: daysAgo(155),
    thumbnailPath: 'thumbnails/reports/interagency-low.jpg',
  },
  {
    name: 'Public Education and Awareness Campaign Strategy',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Create,
    backgroundColor: '#F97316',
    data: generateReportContent('public-education', 'National'),
    responsibilityAreaId: 1,
    layoutId: 15,
    disasterIds: [1, 2, 3],
    createdByUserId: 1,
    reviewedByUserId: 2,
    createdAt: daysAgo(140),
    reviewedAt: daysAgo(135),
    thumbnailPath: 'thumbnails/reports/education-low.jpg',
  },
  {
    name: 'Technology Integration in Disaster Management',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Create,
    backgroundColor: '#6366F1',
    data: generateReportContent('technology-integration', 'National'),
    responsibilityAreaId: 2,
    layoutId: 16,
    disasterIds: [3, 6, 7],
    createdByUserId: 2,
    reviewedByUserId: 3,
    createdAt: daysAgo(110),
    reviewedAt: daysAgo(105),
    thumbnailPath: 'thumbnails/reports/technology-low.jpg',
  },
  {
    name: 'Cross-Border Disaster Response Cooperation',
    status: ApproveStatus.WaitForApprove,
    actionType: DocumentActionType.Create,
    backgroundColor: '#8B5CF6',
    data: generateReportContent('cross-border', 'International'),
    responsibilityAreaId: 3,
    layoutId: 17,
    disasterIds: [1, 4, 8],
    createdByUserId: 3,
    latestRequestedByUserId: 1,
    createdAt: daysAgo(22),
    latestRequestedAt: daysAgo(18),
    thumbnailPath: 'thumbnails/reports/cross-border-low.jpg',
  },
  {
    name: 'Climate Change Adaptation Strategies',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Edit,
    backgroundColor: '#059669',
    data: generateReportContent('climate-adaptation', 'National'),
    responsibilityAreaId: 1,
    layoutId: 18,
    disasterIds: [2, 3, 6, 7],
    createdByUserId: 1,
    reviewedByUserId: 2,
    latestEditedByUserId: 3,
    createdAt: daysAgo(200),
    reviewedAt: daysAgo(195),
    latestEditedAt: daysAgo(180),
    thumbnailPath: 'thumbnails/reports/climate-low.jpg',
  },
  {
    name: 'Legacy Emergency Communication System',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Delete,
    backgroundColor: '#991B1B',
    data: generateReportContent('legacy-system', 'National'),
    responsibilityAreaId: 3,
    layoutId: 19,
    disasterIds: [1],
    createdByUserId: 3,
    reviewedByUserId: 2,
    deletedByUserId: 1,
    createdAt: daysAgo(365),
    reviewedAt: daysAgo(360),
    deletedAt: daysAgo(30),
    shouldSoftDelete: true,
  },
  {
    name: 'Obsolete Weather Monitoring Protocol',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Delete,
    backgroundColor: '#7F1D1D',
    data: generateReportContent('obsolete-protocol', 'Regional'),
    responsibilityAreaId: 2,
    layoutId: 20,
    disasterIds: [3],
    createdByUserId: 2,
    reviewedByUserId: 1,
    deletedByUserId: 3,
    createdAt: daysAgo(400),
    reviewedAt: daysAgo(395),
    deletedAt: daysAgo(45),
    shouldSoftDelete: true,
  },
  {
    name: 'Real-time Multi-Sensor Monitoring Dashboard',
    status: ApproveStatus.WaitForApprove,
    actionType: DocumentActionType.Create,
    backgroundColor: '#0D9488',
    data: generateReportContent('real-time-monitoring', 'National'),
    responsibilityAreaId: 1,
    layoutId: 21,
    disasterIds: [3, 6, 7],
    createdByUserId: 1,
    latestRequestedByUserId: 3,
    createdAt: hoursAgo(8),
    latestRequestedAt: hoursAgo(4),
    thumbnailPath: 'thumbnails/reports/monitoring-dashboard-low.jpg',
  },
  {
    name: 'Next-Generation Mobile Alert System',
    status: ApproveStatus.Draft,
    actionType: DocumentActionType.Draft,
    backgroundColor: '#7C2D12',
    data: generateReportContent('mobile-alert', 'National'),
    responsibilityAreaId: 2,
    layoutId: 22,
    disasterIds: [1, 3, 5],
    createdByUserId: 2,
    createdAt: hoursAgo(18),
  },
  {
    name: 'AI-Powered Disaster Prediction Model',
    status: ApproveStatus.Draft,
    actionType: DocumentActionType.Draft,
    backgroundColor: '#1E40AF',
    data: generateReportContent('ai-prediction', 'National'),
    responsibilityAreaId: 3,
    layoutId: 23,
    disasterIds: [2, 3, 6, 7],
    createdByUserId: 3,
    createdAt: minutesAgo(45),
  },
  {
    name: 'Blockchain-Based Emergency Resource Tracking',
    status: ApproveStatus.WaitForApprove,
    actionType: DocumentActionType.Create,
    backgroundColor: '#7C3AED',
    data: generateReportContent('blockchain-tracking', 'National'),
    responsibilityAreaId: 1,
    layoutId: 24,
    disasterIds: [1, 4, 5],
    createdByUserId: 1,
    latestRequestedByUserId: 2,
    createdAt: hoursAgo(2),
    latestRequestedAt: minutesAgo(30),
    thumbnailPath: 'thumbnails/reports/blockchain-low.jpg',
  },
  {
    name: 'Future Disaster Preparedness Initiative 2025',
    status: ApproveStatus.Draft,
    actionType: DocumentActionType.Draft,
    backgroundColor: '#065F46',
    data: generateReportContent('future-initiative', 'National'),
    responsibilityAreaId: 2,
    layoutId: 1,
    disasterIds: [1, 2, 3, 4, 5],
    createdByUserId: 2,
    createdAt: daysFromNow(30),
  },
  {
    name: 'Advanced Warning System Upgrade Plan',
    status: ApproveStatus.WaitForApprove,
    actionType: DocumentActionType.Create,
    backgroundColor: '#92400E',
    data: generateReportContent('upgrade-plan', 'National'),
    responsibilityAreaId: 3,
    layoutId: 2,
    disasterIds: [6, 7, 8, 9],
    createdByUserId: 3,
    latestRequestedByUserId: 1,
    createdAt: daysFromNow(15),
    latestRequestedAt: daysFromNow(10),
  },
  {
    name: 'Volunteer Management and Training Program',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Create,
    backgroundColor: '#DC2626',
    data: generateReportContent('volunteer-management', 'Regional'),
    responsibilityAreaId: 1,
    layoutId: 3,
    disasterIds: [1, 5],
    createdByUserId: 1,
    reviewedByUserId: 3,
    createdAt: daysAgo(95),
    reviewedAt: daysAgo(90),
    thumbnailPath: 'thumbnails/reports/volunteer-low.jpg',
  },
  {
    name: 'Infrastructure Resilience Assessment Framework',
    status: ApproveStatus.Approved,
    actionType: DocumentActionType.Edit,
    backgroundColor: '#1F2937',
    data: generateReportContent('infrastructure-resilience', 'National'),
    responsibilityAreaId: 2,
    layoutId: 4,
    disasterIds: [1, 2, 4, 7],
    createdByUserId: 2,
    reviewedByUserId: 1,
    latestEditedByUserId: 3,
    createdAt: daysAgo(125),
    reviewedAt: daysAgo(120),
    latestEditedAt: daysAgo(115),
    thumbnailPath: 'thumbnails/reports/infrastructure-low.jpg',
  },
  {
    name: 'Economic Impact Assessment Methodology',
    status: ApproveStatus.Rejected,
    actionType: DocumentActionType.Create,
    backgroundColor: '#BE185D',
    data: generateReportContent('economic-impact', 'National'),
    responsibilityAreaId: 3,
    layoutId: 5,
    disasterIds: [1, 7, 9],
    createdByUserId: 3,
    reviewedByUserId: 2,
    createdAt: daysAgo(35),
    reviewedAt: daysAgo(30),
  },
];
