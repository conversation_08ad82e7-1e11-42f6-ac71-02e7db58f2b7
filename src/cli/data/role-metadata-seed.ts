import { Roles } from '@core/db/entities/roles';

import { RoleName } from '@domain/v1/roles/roles.v1.constant';

export const roleMetadataSeed: Partial<Roles>[] = [
  {
    id: 1,
    name: RoleName.SuperAdmin,
  },
  {
    id: 2,
    name: RoleName.AdminCentral,
  },
  {
    id: 3,
    name: RoleName.AdminProvincial,
  },
  {
    id: 4,
    name: RoleName.UserCentralApprove,
  },
  {
    id: 5,
    name: RoleName.UserCentralCreate,
  },
  {
    id: 6,
    name: RoleName.UserProvincialApprove,
  },
  {
    id: 7,
    name: RoleName.UserProvincialCreate,
  },
];
