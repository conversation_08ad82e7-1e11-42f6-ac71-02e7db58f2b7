import { Permissions } from '@core/db/entities/permissions';
import { permissionOptions } from '@core/global/permission-validator/permission-validator.constants';

export const permissionMetadataSeed: Partial<Permissions>[] = Object.values(
  permissionOptions,
).map((data, idx) => ({
  id: idx + 1,
  type: data.type,
  item: data.item,
  approveStatus: data.approveStatus,
  department: data.department,
  area: data.area,
  description: `Permission for ${data.type} ${data.item} with approvalStatus: ${data.approveStatus}, department: ${data.department}, area: ${data.area}`,
}));
