import { RolePermissions } from '@core/db/entities/role-permissions';

const mapRolePermission = [
  {
    roleId: 2,
    permissionIds: [
      1, 2, 4, 6, 8, 10, 11, 14, 16, 28, 20, 22, 24, 26, 28, 29, 30, 32, 34, 36,
    ],
  },
  {
    roleId: 3,
    permissionIds: [
      1, 3, 5, 7, 9, 10, 12, 13, 15, 17, 19, 21, 23, 25, 27, 28, 29,
    ],
  },
  {
    roleId: 4,
    permissionIds: [1, 9, 10, 17, 21, 23, 25, 27, 29],
  },
  {
    roleId: 5,
    permissionIds: [1, 2, 5, 7, 10, 11, 15, 19, 29],
  },
  {
    roleId: 6,
    permissionIds: [1, 9, 10, 17, 21, 23, 25, 27, 29],
  },
  {
    roleId: 7,
    permissionIds: [1, 3, 5, 7, 10, 12, 13, 15, 19, 29],
  },
];

export const rolePermissionMetadataSeed: Partial<RolePermissions>[] = [
  ...mapRolePermission.flatMap(({ roleId, permissionIds }) =>
    permissionIds.map((permissionId) => ({
      roleId,
      permissionId,
    })),
  ),
];
