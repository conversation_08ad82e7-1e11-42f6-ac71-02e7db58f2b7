import { Command, CommandRunner, Option } from 'nest-commander';

import { DocumentType } from '@domain/v1/documents/documents.v1.constant';

import { reportsMetadataSeed, ReportSeedData } from '../../data/reports-metadata-seed';
import { ReportsCliRepo } from '../reports.cli.repo';

interface CommandOptions {
  amount: number;
  skipValidation: boolean;
  verbose: boolean;
}

interface RequiredEntities {
  users: any[];
  departments: any[];
  disasters: any[];
  layouts: any[];
}

@Command({
  name: 'reports:seed',
  description: 'Create comprehensive seed data for reports system testing',
})
export class ReportsCliSeed extends CommandRunner {
  constructor(private readonly repo: ReportsCliRepo) {
    super();
  }

  async run(_passedParams: string[], options: CommandOptions): Promise<void> {
    const entities = await this.loadRequiredEntities();

    if (!this.validateEntities(entities, options.skipValidation)) {
      return;
    }

    const reportsToCreate = this.getReportsToCreate(options.amount);
    await this.createReports(reportsToCreate, entities);

    console.log('Reports seed completed successfully.');
  }

  private async loadRequiredEntities(): Promise<RequiredEntities> {
    return {
      users: await this.repo.getAllUsers(),
      departments: await this.repo.getAllDepartments(),
      disasters: await this.repo.getAllDisasters(),
      layouts: await this.repo.getAllLayouts(),
    };
  }

  private validateEntities(entities: RequiredEntities, skipValidation: boolean): boolean {
    const validationErrors: string[] = [];

    if (entities.users.length === 0) {
      validationErrors.push('No users found. Please run users:seed first.');
    }
    if (entities.departments.length === 0) {
      validationErrors.push('No departments found. Please run initials:seed first.');
    }
    if (entities.disasters.length === 0) {
      validationErrors.push('No disasters found. Please run initials:seed first.');
    }
    if (entities.layouts.length === 0) {
      validationErrors.push('No layouts found. Please run initials:seed first.');
    }

    if (validationErrors.length > 0 && !skipValidation) {
      validationErrors.forEach((error) => console.log(error));
      return false;
    }

    return true;
  }

  private getReportsToCreate(amount: number): ReportSeedData[] {
    if (amount > 0 && amount < reportsMetadataSeed.length) {
      return reportsMetadataSeed.slice(0, amount);
    }
    return reportsMetadataSeed;
  }

  private async createReports(reportsToCreate: any[], entities: any) {
    for (const reportData of reportsToCreate) {
      try {
        if (!this.validateReportData(reportData, entities)) {
          continue;
        }

        const validDisasterIds = this.getValidDisasterIds(reportData.disasterIds, entities.disasters);
        if (validDisasterIds.length === 0) {
          continue;
        }

        const document = await this.createDocument(reportData);
        await this.createDocumentDisasters(document.id, validDisasterIds, reportData.createdAt);
        await this.createThumbnailIfNeeded(document.id, reportData);
        await this.softDeleteIfNeeded(document.id, reportData.shouldSoftDelete);

      } catch (error) {
        // Continue processing other reports
      }
    }
  }

  private validateReportData(reportData: any, entities: any): boolean {
    const userExists = entities.users.some((u: any) => u.id === reportData.createdByUserId);
    const layoutExists = entities.layouts.some((l: any) => l.id === reportData.layoutId);

    return userExists && layoutExists;
  }

  private getValidDisasterIds(disasterIds: number[], disasters: any[]): number[] {
    return disasterIds.filter(id => disasters.some(d => d.id === id));
  }

  private async createDocument(reportData: any) {
    return await this.repo.createDocument({
      name: reportData.name,
      status: reportData.status,
      actionType: reportData.actionType,
      documentType: DocumentType.Report,
      backgroundColor: reportData.backgroundColor,
      data: reportData.data,
      responsibilityAreaId: reportData.responsibilityAreaId,
      layoutId: reportData.layoutId,
      createdById: reportData.createdByUserId,
      reviewedById: reportData.reviewedByUserId,
      latestRequestedById: reportData.latestRequestedByUserId,
      latestEditedById: reportData.latestEditedByUserId,
      deletedById: reportData.deletedByUserId,
      createdAt: reportData.createdAt,
      reviewedAt: reportData.reviewedAt,
      latestRequestedAt: reportData.latestRequestedAt,
      latestEditedAt: reportData.latestEditedAt,
      deletedAt: reportData.deletedAt,
    });
  }

  private async createDocumentDisasters(documentId: number, disasterIds: number[], createdAt: Date) {
    for (const disasterId of disasterIds) {
      await this.repo.createDocumentDisaster({
        documentId,
        disasterId,
        createdAt,
        updatedAt: createdAt,
      });
    }
  }

  private async createThumbnailIfNeeded(documentId: number, reportData: any) {
    if (!reportData.thumbnailPath) {
      return;
    }

    try {
      const thumbnail = await this.repo.createMainDocumentThumbnail({
        resolutionLowPath: reportData.thumbnailPath,
        resolutionHighPath: reportData.thumbnailPath.replace('-low.', '-high.'),
        createdAt: reportData.createdAt,
        updatedAt: reportData.createdAt,
      });

      await this.repo.updateDocument(documentId, {
        thumbnailId: thumbnail.id,
      });
    } catch (error) {
      // Continue without thumbnail
    }
  }

  private async softDeleteIfNeeded(documentId: number, shouldSoftDelete?: boolean) {
    if (shouldSoftDelete) {
      await this.repo.softDeleteDocument(documentId);
    }
  }

  @Option({
    flags: '-a, --amount [number]',
    defaultValue: '0',
    description: 'Amount of reports to create (0 = all predefined reports)',
  })
  parseAmount(val: string): number {
    return Number(val) || 0;
  }

  @Option({
    flags: '--skip-validation',
    defaultValue: false,
    description: 'Skip validation checks for required entities',
  })
  parseSkipValidation(): boolean {
    return true;
  }

  @Option({
    flags: '-v, --verbose',
    defaultValue: false,
    description: 'Enable verbose output',
  })
  parseVerbose(): boolean {
    return true;
  }
}
