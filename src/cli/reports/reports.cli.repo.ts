import { Injectable } from '@nestjs/common';

import { Departments } from '@core/db/entities/departments';
import { Disasters } from '@core/db/entities/disasters';
import { DocumentDisasters } from '@core/db/entities/document-disasters';
import { DocumentLayouts } from '@core/db/entities/document-layouts';
import { DocumentSnapshotThumbnails } from '@core/db/entities/document-snapshot-thumbnails';
import { DocumentSnapshots } from '@core/db/entities/document-snapshots';
import { DocumentThumbnails } from '@core/db/entities/document-thumbnails';
import { Documents } from '@core/db/entities/documents';
import { Users } from '@core/db/entities/users';
import { BaseRepo } from '@core/shared/common/common.repo';

@Injectable()
export class ReportsCliRepo extends BaseRepo {
  async getAllUsers(): Promise<Users[]> {
    return await this.from(Users).find({
      relations: {
        department: true,
      },
    });
  }

  async getAllDepartments(): Promise<Departments[]> {
    return await this.from(Departments).find();
  }

  async getAllDisasters(): Promise<Disasters[]> {
    return await this.from(Disasters).find();
  }

  async getAllLayouts(): Promise<DocumentLayouts[]> {
    return await this.from(DocumentLayouts).find();
  }

  async createDocument(document: Partial<Documents>): Promise<Documents> {
    const newDocument = this.from(Documents).create(document);
    return await this.from(Documents).save(newDocument);
  }

  async updateDocument(id: number, updates: Partial<Documents>): Promise<void> {
    await this.from(Documents).update(id, updates);
  }

  async createDocumentSnapshot(
    snapshot: Partial<DocumentSnapshots>,
  ): Promise<DocumentSnapshots> {
    const newSnapshot = this.from(DocumentSnapshots).create(snapshot);
    return await this.from(DocumentSnapshots).save(newSnapshot);
  }

  async createDocumentThumbnail(
    thumbnail: Partial<DocumentSnapshotThumbnails>,
  ): Promise<DocumentSnapshotThumbnails> {
    const newThumbnail = this.from(DocumentSnapshotThumbnails).create(
      thumbnail,
    );
    return await this.from(DocumentSnapshotThumbnails).save(newThumbnail);
  }

  async createMainDocumentThumbnail(
    thumbnail: Partial<DocumentThumbnails>,
  ): Promise<DocumentThumbnails> {
    const newThumbnail = this.from(DocumentThumbnails).create(thumbnail);
    return await this.from(DocumentThumbnails).save(newThumbnail);
  }

  async createDocumentDisaster(
    documentDisaster: Partial<DocumentDisasters>,
  ): Promise<DocumentDisasters> {
    const newDocumentDisaster =
      this.from(DocumentDisasters).create(documentDisaster);
    return await this.from(DocumentDisasters).save(newDocumentDisaster);
  }

  async softDeleteDocument(documentId: number): Promise<void> {
    await this.from(Documents).softDelete(documentId);
  }
}
