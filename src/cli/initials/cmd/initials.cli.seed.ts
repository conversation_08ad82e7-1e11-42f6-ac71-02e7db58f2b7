import { random } from 'lodash';
import { Command, CommandRunner } from 'nest-commander';
import {
  departmentMetadataSeeed,
  mapProvinceWithShortName,
} from 'src/cli/data/department-metadata-seed';
import { disasterMetadataSeed } from 'src/cli/data/disaster-metadata-seed';
import { documentThumbnailsMetadataSeed } from 'src/cli/data/document-thumbnail-metadata-seed';
import { layoutMetadataSeed } from 'src/cli/data/layout-metadata-seed';
import { permissionMetadataSeed } from 'src/cli/data/permission-metadata-seed';
import { reportsMetadataSeed } from 'src/cli/data/reports-metadata-seed';
import { roleMetadataSeed } from 'src/cli/data/role-metadata-seed';
import { rolePermissionMetadataSeed } from 'src/cli/data/role-permisssion-metadata-seed';

import { Departments } from '@core/db/entities/departments';
import { Disasters } from '@core/db/entities/disasters';
import { DocumentDisasters } from '@core/db/entities/document-disasters';
import { DocumentLayouts } from '@core/db/entities/document-layouts';
import { DocumentThumbnails } from '@core/db/entities/document-thumbnails';
import { Documents } from '@core/db/entities/documents';
import { Permissions } from '@core/db/entities/permissions';
import { ResponsibilityAreas } from '@core/db/entities/responsibility-area';
import { RolePermissions } from '@core/db/entities/role-permissions';
import { Roles } from '@core/db/entities/roles';
import { UserRoles } from '@core/db/entities/user-roles';
import { Users } from '@core/db/entities/users';
import { DEFAULT_PASSWORD } from '@core/shared/common/common.constant';
import { hashString } from '@core/shared/common/common.crypto';
import { documentFormatDate } from '@core/shared/common/common.dayjs';

import {
  ApproveStatus,
  DocumentActionType,
  DocumentType,
} from '@domain/v1/documents/documents.v1.constant';
import { LayoutDirection } from '@domain/v1/layouts/laytous.v1.constant';
import { RoleName } from '@domain/v1/roles/roles.v1.constant';
import { UserStatus } from '@domain/v1/users/users.v1.constant';

import { InitialsCliRepo } from '../initials.cli.repo';

@Command({
  name: 'initials:seed',
  description: 'Create record in initials table',
})
export class InitialsCliSeed extends CommandRunner {
  superAdmin: Users;

  constructor(private repo: InitialsCliRepo) {
    super();
  }

  async run(_passedParams: string[]): Promise<void> {
    await this._initRoles();
    await this._initPermissions();
    await this._initRolePermissions();
    await this._initDepartmentAndResponsibilityAreas();
    await this._initLayouts();
    await this._initDisasters();
    await this._initThumbnails();
    await this._initUsers();
    await this._initDocuments();
    await this._initComprehensiveReports();
    console.log('Initials seed completed successfully.');
  }

  private async _initRoles() {
    const roles = roleMetadataSeed;

    for (const role of roles) {
      const newRole = this.repo
        .from(Roles)
        .create({ id: role.id, name: role.name });

      await this.repo.from(Roles).save(newRole);
    }
  }

  private async _initPermissions() {
    for (const permission of permissionMetadataSeed) {
      const newPermission = this.repo.from(Permissions).create(permission);
      await this.repo.from(Permissions).save(newPermission);
    }
  }

  private async _initRolePermissions() {
    const rolePermissions = this.repo
      .from(RolePermissions)
      .create(rolePermissionMetadataSeed);
    await this.repo.from(RolePermissions).save(rolePermissions);
  }

  private async _initDepartmentAndResponsibilityAreas() {
    for (const data of departmentMetadataSeeed) {
      const reaponsibilityArea =
        (await this.repo
          .from(ResponsibilityAreas)
          .findOneBy({ code: data.responsibilityArea })) ||
        (await this.repo.from(ResponsibilityAreas).save({
          name: data.responsibilityArea,
          code: data.responsibilityArea,
          shortName: mapProvinceWithShortName[data.responsibilityArea],
        }));

      await this.repo.from(Departments).save<Departments>({
        code: data.code,
        nameTh: data.nameTh,
        nameEn: data.nameEn,
        responsibilityAreaId: reaponsibilityArea.id,
      } as Departments);
    }
  }

  private async _initUsers() {
    const superadminRole = await this.repo
      .from(Roles)
      .findOne({ where: { name: RoleName.SuperAdmin } });

    const adminRole = await this.repo
      .from(Roles)
      .findOne({ where: { name: RoleName.AdminProvincial } });

    const departments = (await this.repo.from(Departments).find()) || [];

    const getRandomDepartmentId = () => {
      if (!departments.length) {
        return undefined;
      }
      const randomIndex = random(0, departments.length - 1);
      return departments[randomIndex].id;
    };

    // Create Users
    const superadmin = this.repo.from(Users).create({
      email: '<EMAIL>',
      username: 'superadmin',
      password: hashString(DEFAULT_PASSWORD),
      status: UserStatus.Active,
      departmentId: getRandomDepartmentId(),
    });

    const superadmin2 = this.repo.from(Users).create({
      email: '<EMAIL>',
      username: 'superadmin2',
      password: hashString(DEFAULT_PASSWORD),
      status: UserStatus.Active,
      departmentId: getRandomDepartmentId(),
    });

    const user = this.repo.from(Users).create({
      email: '<EMAIL>',
      status: UserStatus.Active,
      departmentId: getRandomDepartmentId(),
    });

    await this.repo.from(Users).save([superadmin, superadmin2, user]);

    // Create user roles
    const userRoles = this.repo.from(UserRoles).create([
      {
        user: superadmin,
        role: superadminRole || { id: 1, name: RoleName.SuperAdmin },
      },
      {
        user: superadmin2,
        role: superadminRole || { id: 1, name: RoleName.SuperAdmin },
      },
      {
        user: user,
        role: adminRole || { id: 3, name: RoleName.AdminProvincial },
      },
    ]);

    await this.repo.from(UserRoles).save(userRoles);
  }

  private async _initLayouts() {
    const layouts = layoutMetadataSeed;

    for (const layout of layouts) {
      const newLayout = this.repo.from(DocumentLayouts).create(layout);
      await this.repo.from(DocumentLayouts).save(newLayout);
    }
  }

  private async _initDisasters() {
    const disasters = disasterMetadataSeed;

    for (const disaster of disasters) {
      const newDisaster = this.repo.from(Disasters).create(disaster);
      await this.repo.from(Disasters).save(newDisaster);
    }
  }

  private async _initThumbnails() {
    const thumbnails = documentThumbnailsMetadataSeed;

    for (const thumbnail of thumbnails) {
      const newThumbnail = this.repo.from(DocumentThumbnails).create(thumbnail);
      await this.repo.from(DocumentThumbnails).save(newThumbnail);
    }
  }

  private async _initDocuments() {
    const users = await this.repo.from(Users).find({
      relations: {
        department: {
          responsibilityArea: true,
        },
      },
    });
    const layouts = await this.repo.from(DocumentLayouts).find();
    const disasters = await this.repo.from(Disasters).find();
    const thumbnails = await this.repo.from(DocumentThumbnails).find();

    if (
      !users.length ||
      !layouts.length ||
      !disasters.length ||
      !thumbnails.length
    ) {
      return;
    }

    const verticalLayouts = layouts.filter(
      (l) => l.direction === LayoutDirection.vertical,
    );
    const horizontalLayouts = layouts.filter(
      (l) => l.direction === LayoutDirection.horizontal,
    );

    if (!verticalLayouts.length || !horizontalLayouts.length) {
      return;
    }

    const templateThumbnails = thumbnails.filter((t) =>
      t.resolutionLowPath?.includes('templates'),
    );
    const reportThumbnails = thumbnails.filter((t) =>
      t.resolutionLowPath?.includes('reports'),
    );

    if (!templateThumbnails.length || !reportThumbnails.length) {
      return;
    }

    const numberOfDocuments = 50;
    // create approved templates and reports
    for (let i = 0; i < numberOfDocuments; i++) {
      const numOfTemplates = Math.floor(numberOfDocuments / 2);
      const createUser = users[random(0, users.length - 1)];

      const responsibilityArea = createUser.department.responsibilityArea;

      const responsibilityAreaId = responsibilityArea.id;

      const shortName = responsibilityArea.shortName
        ? `${responsibilityArea.shortName}_`
        : '';

      const actionType =
        i % 2 === 0 ? DocumentActionType.Create : DocumentActionType.Edit;

      const layoutId =
        i % 2 === 0
          ? verticalLayouts[random(0, verticalLayouts.length - 1)].id
          : horizontalLayouts[random(0, horizontalLayouts.length - 1)].id;

      const reviewedById = users[random(0, users.length - 1)].id;
      const createdById = createUser.id;

      const latestEditedById =
        actionType === DocumentActionType.Edit ? createdById : null;
      const latestEditedAt =
        actionType === DocumentActionType.Edit ? new Date() : null;

      if (i < numOfTemplates) {
        const templates = this.repo.from(Documents).create({
          name: `TP_${shortName}${documentFormatDate()}_${i + 1}`,
          status: ApproveStatus.Approved,
          documentType: DocumentType.Template,
          actionType,
          data: {
            title: `Template ${i + 1}`,
            description: `This is a template document for testing purposes.`,
          },
          responsibilityAreaId,
          layoutId,
          thumbnailId:
            templateThumbnails[random(0, templateThumbnails.length - 1)].id,
          createdById,
          reviewedById,
          reviewedAt: new Date(),
          latestRequestedById: createdById,
          latestRequestedAt: new Date(),
          latestEditedById,
          latestEditedAt,
        });

        await this.repo.from(Documents).save(templates);
        await this.repo.from(DocumentDisasters).save({
          documentId: templates.id,
          disasterId: disasters[random(0, disasters.length - 1)].id,
        });
      } else {
        const templates = this.repo.from(Documents).create({
          name: `RP_${shortName}${documentFormatDate()}_${i - numOfTemplates + 1}`,
          status: ApproveStatus.Approved,
          documentType: DocumentType.Report,
          actionType,
          data: {
            title: `Report ${i - numOfTemplates + 1}`,
            description: `This is a report document for testing purposes.`,
          },
          responsibilityAreaId,
          layoutId,
          thumbnailId:
            reportThumbnails[random(0, reportThumbnails.length - 1)].id,
          createdById,
          reviewedById,
          reviewedAt: new Date(),
          latestRequestedById: createdById,
          latestRequestedAt: new Date(),
          latestEditedById,
          latestEditedAt,
        });

        await this.repo.from(Documents).save(templates);
        await this.repo.from(DocumentDisasters).save({
          documentId: templates.id,
          disasterId: disasters[random(0, disasters.length - 1)].id,
        });
      }
    }
  }

  private async _initComprehensiveReports() {
    console.log('🚀 Creating comprehensive reports seed data...');

    // Get all required entities
    const users = await this.repo.from(Users).find({
      relations: {
        department: {
          responsibilityArea: true,
        },
      },
    });
    const disasters = await this.repo.from(Disasters).find();
    const layouts = await this.repo.from(DocumentLayouts).find();

    if (users.length === 0 || disasters.length === 0 || layouts.length === 0) {
      console.log('⚠️  Skipping comprehensive reports - missing required data');
      return;
    }

    let createdCount = 0;
    let approvedCount = 0;
    let pendingCount = 0;
    let softDeletedCount = 0;

    for (const reportData of reportsMetadataSeed) {
      try {
        // Validate user IDs exist
        const createdByUser = users.find(
          (u) => u.id === reportData.createdByUserId,
        );
        if (!createdByUser) {
          console.log(
            `⚠️  Skipping report "${reportData.name}" - createdByUserId ${reportData.createdByUserId} not found`,
          );
          continue;
        }

        // Validate layout exists
        const layout = layouts.find((l) => l.id === reportData.layoutId);
        if (!layout) {
          console.log(
            `⚠️  Skipping report "${reportData.name}" - layoutId ${reportData.layoutId} not found`,
          );
          continue;
        }

        // Validate disaster IDs exist
        const validDisasterIds = reportData.disasterIds.filter((id) =>
          disasters.some((d) => d.id === id),
        );
        if (validDisasterIds.length === 0) {
          console.log(
            `⚠️  Skipping report "${reportData.name}" - no valid disaster IDs found`,
          );
          continue;
        }

        // Create the document
        const document = await this.repo.from(Documents).save({
          name: reportData.name,
          status: reportData.status,
          actionType: reportData.actionType,
          documentType: DocumentType.Report,
          backgroundColor: reportData.backgroundColor,
          data: reportData.data,
          responsibilityAreaId: reportData.responsibilityAreaId,
          layoutId: reportData.layoutId,
          createdById: reportData.createdByUserId,
          reviewedById: reportData.reviewedByUserId,
          latestRequestedById: reportData.latestRequestedByUserId,
          latestEditedById: reportData.latestEditedByUserId,
          createdAt: reportData.createdAt,
          reviewedAt: reportData.reviewedAt,
          latestRequestedAt: reportData.latestRequestedAt,
          latestEditedAt: reportData.latestEditedAt,
        });

        // Create document-disaster relationships
        for (const disasterId of validDisasterIds) {
          await this.repo.from(DocumentDisasters).save({
            documentId: document.id,
            disasterId: disasterId,
            createdAt: reportData.createdAt,
            updatedAt: reportData.createdAt,
          });
        }

        // Soft delete if specified
        if (reportData.shouldSoftDelete) {
          await this.repo.from(Documents).softDelete(document.id);
          softDeletedCount++;
        }

        createdCount++;

        // Count by status for summary
        if (reportData.status === 'approved') {
          approvedCount++;
        } else {
          pendingCount++;
        }

        console.log(
          `✅ Created comprehensive report: "${reportData.name}" (${reportData.status})`,
        );
      } catch (error) {
        console.log(
          `❌ Failed to create report "${reportData.name}":`,
          error instanceof Error ? error.message : String(error),
        );
      }
    }

    console.log('\n📈 Comprehensive Reports Summary:');
    console.log(`   📝 Total Created: ${createdCount}`);
    console.log(`   ✅ Approved: ${approvedCount}`);
    console.log(`   ⏳ Pending: ${pendingCount}`);
    console.log(`   🗑️  Soft Deleted: ${softDeletedCount}`);
    console.log('🚀 Comprehensive reports seed completed!');
  }
}
