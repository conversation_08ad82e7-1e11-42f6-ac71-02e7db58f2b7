import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';

import { RolePermissions } from './role-permissions';

@Entity('permissions')
export class Permissions {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar' })
  type: string;

  @Column({ type: 'varchar' })
  item: string;

  @Column({ type: 'varchar', nullable: true })
  approveStatus: string | null;

  @Column({ type: 'varchar', nullable: true })
  department: string | null;

  @Column({ type: 'varchar', nullable: true })
  area: string | null;

  @Column({ type: 'varchar', nullable: true })
  description: string | null;

  @OneToMany(() => RolePermissions, (rp) => rp.permission)
  rolePermissions: RolePermissions[];

  @OneToMany(() => RolePermissions, (rp) => rp.permission)
  userPermissions: RolePermissions[];
}
