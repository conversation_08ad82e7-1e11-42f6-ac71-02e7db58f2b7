import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { DocumentSnapshots } from './document-snapshots';
import { Documents } from './documents';
import { Users } from './users';

@Entity('approvals')
export class Approvals {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar' })
  reason: string;

  @Column({ type: 'int', name: 'document_id' })
  documentId: number;

  @Column({ type: 'int', name: 'document_snapshot_id' })
  documentSnapshotId: number;

  @Column({ type: 'varchar' })
  status: string;

  @Column({ type: 'varchar', name: 'action_type' })
  actionType: string;

  @Column({ type: 'int', name: 'reviewed_by_id' })
  reviewedById: number;

  @Column({ type: 'int', name: 'requested_by_id' })
  requestedById: number;

  @Column({ type: 'timestamp with time zone', name: 'reviewed_at' })
  reviewedAt: Date;

  @Column({ type: 'timestamp with time zone', name: 'requested_at' })
  requestedAt: Date;

  @Column({ type: 'timestamp with time zone', name: 'created_at' })
  createdAt: Date;

  @Column({ type: 'timestamp with time zone', name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => Documents, (document) => document.approvals)
  @JoinColumn({ name: 'document_id' })
  document: Documents;

  @ManyToOne(
    () => DocumentSnapshots,
    (documentSnapshot) => documentSnapshot.approvals,
  )
  @JoinColumn({ name: 'document_snapshot_id' })
  documentSnapshot: DocumentSnapshots;

  @ManyToOne(() => Users, (user) => user.approvalReviews)
  @JoinColumn({ name: 'reviewed_by_id' })
  reviewedBy: Users;

  @ManyToOne(() => Users, (user) => user.approvalRequests)
  @JoinColumn({ name: 'requested_by_id' })
  requestedBy: Users;
}
