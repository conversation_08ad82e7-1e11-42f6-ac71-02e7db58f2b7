import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveFilePathAtDoucumentThumbnailTable1750141977662
  implements MigrationInterface
{
  name = 'RemoveFilePathAtDoucumentThumbnailTable1750141977662';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "document_thumbnails" DROP COLUMN "resolution_low"`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_thumbnails" DROP COLUMN "resolution_high"`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_thumbnails" DROP COLUMN "file_path"`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_thumbnails" ADD "resolution_low_path" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_thumbnails" ADD "resolution_high_path" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "document_thumbnails" DROP COLUMN "resolution_high_path"`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_thumbnails" DROP COLUMN "resolution_low_path"`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_thumbnails" ADD "file_path" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_thumbnails" ADD "resolution_high" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_thumbnails" ADD "resolution_low" character varying NOT NULL`,
    );
  }
}
