import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameColumnupdatedById1750227802562
  implements MigrationInterface
{
  name = 'RenameColumnupdatedById1750227802562';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "documents" RENAME COLUMN "updated_by_id" TO "latest_edited_at"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "documents" RENAME COLUMN "latest_edited_at" TO "updated_by_id"`,
    );
  }
}
