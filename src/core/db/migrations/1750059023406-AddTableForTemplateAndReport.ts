import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTableForTemplateAndReport1750059023406
  implements MigrationInterface
{
  name = 'AddTableForTemplateAndReport1750059023406';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "document_snapshot_thumbnails" ("id" SERIAL NOT NULL, "file_path" character varying NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_1b150cc5da3eb3f41ddf79c14f8" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "document_disasters" ("id" SERIAL NOT NULL, "document_id" integer NOT NULL, "disaster_id" integer NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_47367d26e6f5410f586b2a9d0cc" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "disasters" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_1c8d79629e142ad7e562e81bc4d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "document_layouts" ("id" SERIAL NOT NULL, "direction" character varying NOT NULL, "filepath" character varying NOT NULL, "style" character varying, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_d400be84c6ca9766afbd364cd8b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "document_thumbnails" ("id" SERIAL NOT NULL, "file_path" character varying NOT NULL, "resolution_low" character varying NOT NULL, "resolution_high" character varying NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_934a644c0ccb305e2df0956c4a1" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "documents" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "document_type" character varying NOT NULL, "status" character varying NOT NULL, "action_type" character varying NOT NULL, "responsibility_area_id" character varying NOT NULL, "layout_id" integer NOT NULL, "data" json, "thumbnail_id" integer, "reviewed_by_id" integer, "latest_requested_by_id" integer, "latest_edited_by_id" integer, "deleted_by_id" integer, "created_by_id" integer NOT NULL, "reviewed_at" TIME WITH TIME ZONE, "requested_at" TIME WITH TIME ZONE, "updated_by_id" TIME WITH TIME ZONE, "deleted_at" TIMESTAMP WITH TIME ZONE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "responsibilityAreaId" integer, CONSTRAINT "PK_ac51aa5181ee2036f5ca482857c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "document_snapshots" ("id" SERIAL NOT NULL, "document_id" integer NOT NULL, "thumbnail_id" integer NOT NULL, "data" json NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_c8bd7cb08190bbcc4ace233da30" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "approvals" ("id" SERIAL NOT NULL, "reason" character varying NOT NULL, "document_id" integer NOT NULL, "document_snapshot_id" integer NOT NULL, "status" character varying NOT NULL, "action_type" character varying NOT NULL, "reviewed_by_id" integer NOT NULL, "requested_by_id" integer NOT NULL, "reviewed_at" TIMESTAMP WITH TIME ZONE NOT NULL, "requested_at" TIMESTAMP WITH TIME ZONE NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, CONSTRAINT "PK_690417aaefa84d18b1a59e2a499" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_disasters" ADD CONSTRAINT "FK_eb03e0eb7d80191b2e4d9eaa8f9" FOREIGN KEY ("document_id") REFERENCES "documents"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_disasters" ADD CONSTRAINT "FK_33f30a4b00e39861284da9eac35" FOREIGN KEY ("disaster_id") REFERENCES "disasters"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" ADD CONSTRAINT "FK_6206ad0d70184d10beb0c59429f" FOREIGN KEY ("layout_id") REFERENCES "document_layouts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" ADD CONSTRAINT "FK_9f5f048d43e8d63201ebaee2f74" FOREIGN KEY ("responsibilityAreaId") REFERENCES "responsibility_area"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" ADD CONSTRAINT "FK_27ac5a6d61ae1943c10919a8674" FOREIGN KEY ("thumbnail_id") REFERENCES "document_thumbnails"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" ADD CONSTRAINT "FK_8c4ee210e83a863fae3ab18cd86" FOREIGN KEY ("reviewed_by_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" ADD CONSTRAINT "FK_78a0175b5322b33adda4a56eb51" FOREIGN KEY ("latest_requested_by_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" ADD CONSTRAINT "FK_e3f7614e0631872b743dee51466" FOREIGN KEY ("latest_edited_by_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" ADD CONSTRAINT "FK_f1c4f762eb3805a26aa07fe7ea9" FOREIGN KEY ("deleted_by_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" ADD CONSTRAINT "FK_7f46f4f77acde1dcedba64cb220" FOREIGN KEY ("created_by_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_snapshots" ADD CONSTRAINT "FK_c0708186324cc9c27c7d7a54c75" FOREIGN KEY ("document_id") REFERENCES "documents"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_snapshots" ADD CONSTRAINT "FK_b30d7a7918e9a068a5253448a07" FOREIGN KEY ("thumbnail_id") REFERENCES "document_snapshot_thumbnails"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approvals" ADD CONSTRAINT "FK_ec48d6bdb662e3b718b64d2f6f0" FOREIGN KEY ("document_id") REFERENCES "documents"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approvals" ADD CONSTRAINT "FK_3e7b72736c0be55f5b36974c911" FOREIGN KEY ("document_snapshot_id") REFERENCES "document_snapshots"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approvals" ADD CONSTRAINT "FK_44632947ded3273c38e9c7136e1" FOREIGN KEY ("reviewed_by_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "approvals" ADD CONSTRAINT "FK_7056401cd7d854d4288449df707" FOREIGN KEY ("requested_by_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "approvals" DROP CONSTRAINT "FK_7056401cd7d854d4288449df707"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approvals" DROP CONSTRAINT "FK_44632947ded3273c38e9c7136e1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approvals" DROP CONSTRAINT "FK_3e7b72736c0be55f5b36974c911"`,
    );
    await queryRunner.query(
      `ALTER TABLE "approvals" DROP CONSTRAINT "FK_ec48d6bdb662e3b718b64d2f6f0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_snapshots" DROP CONSTRAINT "FK_b30d7a7918e9a068a5253448a07"`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_snapshots" DROP CONSTRAINT "FK_c0708186324cc9c27c7d7a54c75"`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" DROP CONSTRAINT "FK_7f46f4f77acde1dcedba64cb220"`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" DROP CONSTRAINT "FK_f1c4f762eb3805a26aa07fe7ea9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" DROP CONSTRAINT "FK_e3f7614e0631872b743dee51466"`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" DROP CONSTRAINT "FK_78a0175b5322b33adda4a56eb51"`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" DROP CONSTRAINT "FK_8c4ee210e83a863fae3ab18cd86"`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" DROP CONSTRAINT "FK_27ac5a6d61ae1943c10919a8674"`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" DROP CONSTRAINT "FK_9f5f048d43e8d63201ebaee2f74"`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" DROP CONSTRAINT "FK_6206ad0d70184d10beb0c59429f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_disasters" DROP CONSTRAINT "FK_33f30a4b00e39861284da9eac35"`,
    );
    await queryRunner.query(
      `ALTER TABLE "document_disasters" DROP CONSTRAINT "FK_eb03e0eb7d80191b2e4d9eaa8f9"`,
    );
    await queryRunner.query(`DROP TABLE "approvals"`);
    await queryRunner.query(`DROP TABLE "document_snapshots"`);
    await queryRunner.query(`DROP TABLE "documents"`);
    await queryRunner.query(`DROP TABLE "document_thumbnails"`);
    await queryRunner.query(`DROP TABLE "document_layouts"`);
    await queryRunner.query(`DROP TABLE "disasters"`);
    await queryRunner.query(`DROP TABLE "document_disasters"`);
    await queryRunner.query(`DROP TABLE "document_snapshot_thumbnails"`);
  }
}
