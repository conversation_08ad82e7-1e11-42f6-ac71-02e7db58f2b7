import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCreatedAtAndUpdateAtInAgenciesAndProvincesTable1747718177091
  implements MigrationInterface
{
  name = 'UpdateCreatedAtAndUpdateAtInAgenciesAndProvincesTable1747718177091';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "provinces" DROP COLUMN "created_at"`);
    await queryRunner.query(
      `ALTER TABLE "provinces" ADD "created_at" TIME WITH TIME ZONE NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(`ALTER TABLE "provinces" DROP COLUMN "updated_at"`);
    await queryRunner.query(
      `ALTER TABLE "provinces" ADD "updated_at" TIME WITH TIME ZONE NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(`ALTER TABLE "agencies" DROP COLUMN "created_at"`);
    await queryRunner.query(
      `ALTER TABLE "agencies" ADD "created_at" TIME WITH TIME ZONE NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(`ALTER TABLE "agencies" DROP COLUMN "updated_at"`);
    await queryRunner.query(
      `ALTER TABLE "agencies" ADD "updated_at" TIME WITH TIME ZONE NOT NULL DEFAULT now()`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "agencies" DROP COLUMN "updated_at"`);
    await queryRunner.query(
      `ALTER TABLE "agencies" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(`ALTER TABLE "agencies" DROP COLUMN "created_at"`);
    await queryRunner.query(
      `ALTER TABLE "agencies" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(`ALTER TABLE "provinces" DROP COLUMN "updated_at"`);
    await queryRunner.query(
      `ALTER TABLE "provinces" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(`ALTER TABLE "provinces" DROP COLUMN "created_at"`);
    await queryRunner.query(
      `ALTER TABLE "provinces" ADD "created_at" TIMESTAMP NOT NULL DEFAULT now()`,
    );
  }
}
