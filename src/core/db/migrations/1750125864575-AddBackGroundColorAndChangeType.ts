import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBackGroundColorAndChangeType1750125864575
  implements MigrationInterface
{
  name = 'AddBackGroundColorAndChangeType1750125864575';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "documents" ADD "background_color" character varying NOT NULL DEFAULT '#FFFFFF'`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" DROP COLUMN "reviewed_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" ADD "reviewed_at" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" DROP COLUMN "requested_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" ADD "requested_at" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" DROP COLUMN "updated_by_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" ADD "updated_by_id" TIMESTAMP WITH TIME ZONE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "documents" DROP COLUMN "updated_by_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" ADD "updated_by_id" TIME WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" DROP COLUMN "requested_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" ADD "requested_at" TIME WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" DROP COLUMN "reviewed_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" ADD "reviewed_at" TIME WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "documents" DROP COLUMN "background_color"`,
    );
  }
}
