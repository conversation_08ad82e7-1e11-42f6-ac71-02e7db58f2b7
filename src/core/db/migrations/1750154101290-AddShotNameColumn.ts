import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddShotNameColumn1750154101290 implements MigrationInterface {
  name = 'AddShotNameColumn1750154101290';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "responsibility_area" ADD "short_name" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "responsibility_area" DROP COLUMN "short_name"`,
    );
  }
}
