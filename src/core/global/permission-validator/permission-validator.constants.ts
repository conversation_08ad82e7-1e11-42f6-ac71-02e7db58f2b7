import {
  PermissionsApproveStatus,
  PermissionsArea,
  PermissionsDepartment,
  PermissionsItem,
  PermissionsType,
} from '@domain/v1/permissions/permissions.v1.constant';

export const permissionValidatorError = {
  PermissionDenied: 'permissionDenied',
};

export const permissionOptions = {
  readTemplate: {
    type: PermissionsType.Read,
    item: PermissionsItem.Template,
    approveStatus: null,
    department: PermissionsDepartment.All,
    area: null,
  },
  createTemplateAllArea: {
    type: PermissionsType.Create,
    item: PermissionsItem.Template,
    approveStatus: null,
    department: null,
    area: PermissionsArea.All,
  },
  createTemplateOwnArea: {
    type: PermissionsType.Create,
    item: PermissionsItem.Template,
    approveStatus: null,
    department: null,
    area: PermissionsArea.Own,
  },
  editTemplateApprovedAllDept: {
    type: PermissionsType.Edit,
    item: PermissionsItem.Template,
    approveStatus: PermissionsApproveStatus.Approved,
    department: PermissionsDepartment.All,
    area: null,
  },
  editTemplateApprovedOwnDept: {
    type: PermissionsType.Edit,
    item: PermissionsItem.Template,
    approveStatus: PermissionsApproveStatus.Approved,
    department: PermissionsDepartment.Own,
    area: null,
  },
  deleteTemplateNotApprovedAllDept: {
    type: PermissionsType.Delete,
    item: PermissionsItem.Template,
    approveStatus: PermissionsApproveStatus.No,
    department: PermissionsDepartment.All,
    area: null,
  },
  deleteTemplateNotApprovedOwnDept: {
    type: PermissionsType.Delete,
    item: PermissionsItem.Template,
    approveStatus: PermissionsApproveStatus.No,
    department: PermissionsDepartment.Own,
    area: null,
  },
  deleteTemplateApprovedAllDept: {
    type: PermissionsType.Delete,
    item: PermissionsItem.Template,
    approveStatus: PermissionsApproveStatus.Approved,
    department: PermissionsDepartment.All,
    area: null,
  },
  deleteTemplateApprovedOwnDept: {
    type: PermissionsType.Delete,
    item: PermissionsItem.Template,
    approveStatus: PermissionsApproveStatus.Approved,
    department: PermissionsDepartment.Own,
    area: null,
  },
  readReportAllDept: {
    type: PermissionsType.Read,
    item: PermissionsItem.Report,
    approveStatus: null,
    department: PermissionsDepartment.All,
    area: null,
  },
  createReportAllArea: {
    type: PermissionsType.Create,
    item: PermissionsItem.Report,
    approveStatus: null,
    department: null,
    area: PermissionsArea.All,
  },
  createReportOwnArea: {
    type: PermissionsType.Create,
    item: PermissionsItem.Report,
    approveStatus: null,
    department: null,
    area: PermissionsArea.Own,
  },
  editReportApprovedOwnDept: {
    type: PermissionsType.Edit,
    item: PermissionsItem.Report,
    approveStatus: PermissionsApproveStatus.Approved,
    department: PermissionsDepartment.Own,
    area: null,
  },
  deleteReportNotApprovedAllDept: {
    type: PermissionsType.Delete,
    item: PermissionsItem.Report,
    approveStatus: PermissionsApproveStatus.No,
    department: PermissionsDepartment.All,
    area: null,
  },
  deleteReportNotApprovedOwnDept: {
    type: PermissionsType.Delete,
    item: PermissionsItem.Report,
    approveStatus: PermissionsApproveStatus.No,
    department: PermissionsDepartment.Own,
    area: null,
  },
  deleteReportApprovedAllDept: {
    type: PermissionsType.Delete,
    item: PermissionsItem.Report,
    approveStatus: PermissionsApproveStatus.Approved,
    department: PermissionsDepartment.All,
    area: null,
  },
  deleteReportApprovedOwnDept: {
    type: PermissionsType.Delete,
    item: PermissionsItem.Report,
    approveStatus: PermissionsApproveStatus.Approved,
    department: PermissionsDepartment.Own,
    area: null,
  },
  editReportAllDept: {
    type: PermissionsType.Edit,
    item: PermissionsItem.Report,
    approveStatus: null,
    department: PermissionsDepartment.All,
    area: null,
  },
  editReportOwnDept: {
    type: PermissionsType.Edit,
    item: PermissionsItem.Report,
    approveStatus: null,
    department: PermissionsDepartment.Own,
    area: null,
  },
  readTemplateNotApprovedAllDept: {
    type: PermissionsType.Read,
    item: PermissionsItem.Template,
    approveStatus: PermissionsApproveStatus.No,
    department: PermissionsDepartment.All,
    area: null,
  },
  readTemplateNotApprovedOwnDept: {
    type: PermissionsType.Read,
    item: PermissionsItem.Template,
    approveStatus: PermissionsApproveStatus.No,
    department: PermissionsDepartment.Own,
    area: null,
  },
  readReportNotApprovedAllDept: {
    type: PermissionsType.Read,
    item: PermissionsItem.Report,
    approveStatus: PermissionsApproveStatus.No,
    department: PermissionsDepartment.All,
    area: null,
  },
  readReportNotApprovedOwnDept: {
    type: PermissionsType.Read,
    item: PermissionsItem.Report,
    approveStatus: PermissionsApproveStatus.No,
    department: PermissionsDepartment.Own,
    area: null,
  },
  approveTemplateAllDept: {
    type: PermissionsType.Approve,
    item: PermissionsItem.Template,
    approveStatus: null,
    department: PermissionsDepartment.All,
    area: null,
  },
  approveTemplateOwnDept: {
    type: PermissionsType.Approve,
    item: PermissionsItem.Template,
    approveStatus: null,
    department: PermissionsDepartment.Own,
    area: null,
  },
  approveReportAllDept: {
    type: PermissionsType.Approve,
    item: PermissionsItem.Report,
    approveStatus: null,
    department: PermissionsDepartment.All,
    area: null,
  },
  approveReportOwnDept: {
    type: PermissionsType.Approve,
    item: PermissionsItem.Report,
    approveStatus: null,
    department: PermissionsDepartment.Own,
    area: null,
  },
  readUserSystemActivity: {
    type: PermissionsType.Read,
    item: PermissionsItem.SystemUsageHistory,
    approveStatus: null,
    department: null,
    area: null,
  },
  exportReportAllDept: {
    type: PermissionsType.Export,
    item: PermissionsItem.Report,
    approveStatus: null,
    department: PermissionsDepartment.All,
    area: null,
  },
  inviteUserAllDept: {
    type: PermissionsType.Invite,
    item: PermissionsItem.User,
    approveStatus: null,
    department: PermissionsDepartment.All,
    area: null,
  },
  inviteUserOwnDept: {
    type: PermissionsType.Invite,
    item: PermissionsItem.User,
    approveStatus: null,
    department: PermissionsDepartment.Own,
    area: null,
  },
  deleteUserAllDept: {
    type: PermissionsType.Delete,
    item: PermissionsItem.User,
    approveStatus: null,
    department: PermissionsDepartment.All,
    area: null,
  },
  deleteUserOwnDept: {
    type: PermissionsType.Delete,
    item: PermissionsItem.User,
    approveStatus: null,
    department: PermissionsDepartment.Own,
    area: null,
  },
  editUserAllDept: {
    type: PermissionsType.Edit,
    item: PermissionsItem.User,
    approveStatus: null,
    department: PermissionsDepartment.All,
    area: null,
  },
  editUserOwnDept: {
    type: PermissionsType.Edit,
    item: PermissionsItem.User,
    approveStatus: null,
    department: PermissionsDepartment.Own,
    area: null,
  },
  readUserAllDept: {
    type: PermissionsType.Read,
    item: PermissionsItem.User,
    approveStatus: null,
    department: PermissionsDepartment.All,
    area: null,
  },
  readUserOwnDept: {
    type: PermissionsType.Read,
    item: PermissionsItem.User,
    approveStatus: null,
    department: PermissionsDepartment.Own,
    area: null,
  },
};
