import { Provider } from '@nestjs/common';

import { RedisCacheProvider } from './cache/cache.provider';
import { CacheService } from './cache/cache.service';
import { DxService } from './dx/dx.service';
import { NodeMailerProvider } from './email/email.provider';
import { EmailService } from './email/email.service';
import { LoggerService } from './logger/logger.service';
import { MinioProvider } from './minio/minio.provider';
import { MinioService } from './minio/minio.service';
import { PermissionValidatorService } from './permission-validator/permission-validator.service';
import { TransactionService } from './transaction/transaction.service';

export const GLOBAL_PROVIDER: Provider[] = [
  // third party
  NodeMailerProvider,
  RedisCacheProvider,
  MinioProvider,

  // provider
  LoggerService,
  TransactionService,
  EmailService,
  CacheService,
  MinioService,
  DxService,
  PermissionValidatorService,
];
