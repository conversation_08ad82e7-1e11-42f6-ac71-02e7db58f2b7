import { NestFactory } from '@nestjs/core';
import * as cors from 'cors';

import { config } from '@core/config';
import { coreLogger } from '@core/shared/common/common.logger';
import { setupApp, setupSwagger } from '@core/shared/http/http.setup';

import { MainAppModule } from './app.module';

const appConfig = config().app;

async function bootstrap() {
  const app = await NestFactory.create(MainAppModule, {
    logger: coreLogger(appConfig),
  });

  app.use((req, res, next) => {
    const appCors = process.env.APP_CORS || '';
    const allowPaths = process.env.APP_CORS_ALLOW_PATHS || '';

    const whitelistCors = appCors ? appCors.split(',') : null;
    const whitelistPaths = allowPaths ? allowPaths.split(',') : [];

    const allowAllPaths = [...whitelistPaths];

    const corsOptions: cors.CorsOptions = {
      origin: (origin, callback) => {
        const isAllowed =
          !whitelistCors ||
          allowAllPaths.some((path) => req.path.includes(path)) ||
          !origin ||
          whitelistCors.includes(origin);

        return isAllowed
          ? callback(null, true)
          : callback(new Error('Not allowed by CORS'));
      },
      credentials: true,
      methods: ['GET', 'POST', 'PATCH', 'DELETE', 'OPTIONS'],
    };

    cors(corsOptions)(req, res, next);
  });

  setupApp(app);

  if (appConfig.enableSwagger) {
    setupSwagger(app);
  }

  await app.listen(appConfig.port);
}
bootstrap();
