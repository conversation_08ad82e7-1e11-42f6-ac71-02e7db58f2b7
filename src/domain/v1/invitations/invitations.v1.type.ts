import { InvitationError } from './invitations.v1.constant';

export interface NewInvitation {
  firstname: string;
  lastname: string;
  email: string;
  role: string;
  departmentCode: string;
}

export interface VerifyifyInvitation {
  token: string;
}

export interface CancelInvitation {
  email: string;
}

export type InvitationErrorType =
  | typeof InvitationError.Validation
  | typeof InvitationError.EmailAlreadyInvited
  | typeof InvitationError.DepartmentNotFound
  | typeof InvitationError.RoleNotFound;
