import { Body, Controller, Patch, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiTags } from '@nestjs/swagger';

import { Users } from '@core/db/entities/users';
import { ReqUser, UsePublic } from '@core/middleware/jwt/jwt.common';
import { errIs } from '@core/shared/common/common.neverthrow';
import { ApiException } from '@core/shared/http/http.exception';

import { CancelInvitationV1HttpDto } from '@domain/v1/invitations/handler/http/dto/delete-invations.v1.http.dto';

import { InvitationsV1Service } from '../../invitations.v1.service';
import { PostInvitationV1HttpDto } from './dto/post-invations.v1.http.dto';
import { PostVerifyInvitation } from './dto/post-verify-invations.v1.http.dto';

@ApiTags('InvitationControllerV1')
@ApiBearerAuth()
@Controller({ path: 'invitations', version: '1' })
export class InvitationsV1HttpController {
  constructor(private service: InvitationsV1Service) {}

  @Post()
  @ApiBody({
    type: PostInvitationV1HttpDto,
    isArray: true,
    description: 'Array of invitations to create',
  })
  async postInvitation(
    @Body() body: PostInvitationV1HttpDto[],
    @ReqUser() reqUser: Users,
  ): Promise<any> {
    const r = await this.service.postInvitation(body, reqUser);

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data,
      }),
      (e) => {
        if (errIs(e, e.key)) {
          throw new ApiException(e, 400);
        }

        throw new ApiException(e, 500);
      },
    );
  }

  @UsePublic()
  @Post('verify')
  async postVerifyInvitation(@Body() body: PostVerifyInvitation): Promise<any> {
    const r = await this.service.postVerifyInvitation(body);

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data,
      }),
      (e) => {
        if (errIs(e, e.key)) {
          throw new ApiException(e, 400);
        }
        throw new ApiException(e, 500);
      },
    );
  }

  @Patch()
  @ApiBody({
    type: CancelInvitationV1HttpDto,
    isArray: true,
    description: 'Array of invitations to cancel',
  })
  async patchCancelInvitation(
    @Body() body: CancelInvitationV1HttpDto[],
  ): Promise<any> {
    const r = await this.service.patchCancelInvitation(body);

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data,
      }),
      (e) => {
        if (errIs(e, e.key)) {
          throw new ApiException(e, 400);
        }
        throw new ApiException(e, 500);
      },
    );
  }
}
