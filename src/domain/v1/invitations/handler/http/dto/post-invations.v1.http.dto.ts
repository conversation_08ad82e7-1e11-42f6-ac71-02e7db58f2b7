import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsString } from 'class-validator';

import { IStandardSingleApiResponse } from '@core/shared/http/http.standard';

import { RoleName } from '@domain/v1/roles/roles.v1.constant';

import { NewInvitation } from '../../../invitations.v1.type';

export class PostInvitationV1HttpDto implements NewInvitation {
  @ApiProperty({ example: 'John' })
  @IsString()
  firstname: string;

  @ApiProperty({ example: 'Doe' })
  @IsString()
  lastname: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsString()
  @IsEmail()
  email: string;

  @ApiProperty({ example: RoleName.AdminCentral })
  @IsEnum(RoleName)
  role: string;

  @ApiProperty({ example: 'กค.' })
  @IsString()
  departmentCode: string;
}

class PostInvitationV1HttpData {}

export class PostInvitationV1HttpResponse
  implements IStandardSingleApiResponse
{
  success: boolean;
  key: string;
  data: PostInvitationV1HttpData;
}
