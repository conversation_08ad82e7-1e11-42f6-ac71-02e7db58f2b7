import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

import { IStandardSingleApiResponse } from '@core/shared/http/http.standard';

import { VerifyifyInvitation } from '@domain/v1/invitations/invitations.v1.type';

export class PostVerifyInvitation implements VerifyifyInvitation {
  @ApiProperty({ example: '1234567890' })
  @IsString()
  token: string;
}

class PostVerifyInvitationData {}

export class PostVerifyInvitationResponse
  implements IStandardSingleApiResponse
{
  success: boolean;
  key: string;
  data: PostVerifyInvitationData;
}
