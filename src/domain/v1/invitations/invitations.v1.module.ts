import { Module } from '@nestjs/common';

import { UsersV1Repo } from '../users/users.v1.repo';
import { InvitationsV1HttpController } from './handler/http/invitations.v1.http.controller';
import { InvitationsV1Repo } from './invitations.v1.repo';
import { InvitationsV1Service } from './invitations.v1.service';

@Module({
  providers: [InvitationsV1Service, InvitationsV1Repo, UsersV1Repo],
  controllers: [InvitationsV1HttpController],
})
export class InvitationsV1Module {}
