import { ConfigService } from '@nestjs/config';
import { mock } from 'jest-mock-extended';

import { Departments } from '@core/db/entities/departments';
import { Invitations } from '@core/db/entities/invitations';
import { Roles } from '@core/db/entities/roles';
import { Users } from '@core/db/entities/users';
import { DxService } from '@core/global/dx/dx.service';
import { EmailService } from '@core/global/email/email.service';
import tzDayjs from '@core/shared/common/common.dayjs';
import { Ok, errIs } from '@core/shared/common/common.neverthrow';
import { createTestingModule } from '@core/test/test-util/test-util.common';

import { RoleName } from '@domain/v1/roles/roles.v1.constant';
import { UserStatus } from '@domain/v1/users/users.v1.constant';

import { InvitationError, InviteStatus } from '../invitations.v1.constant';
import { InvitationsV1Module } from '../invitations.v1.module';
import { InvitationsV1Repo } from '../invitations.v1.repo';
import { InvitationsV1Service } from '../invitations.v1.service';

describe('InvitationsService', () => {
  const repo = mock<InvitationsV1Repo>();
  const config = mock<ConfigService>();
  const dx = mock<DxService>();
  const email = mock<EmailService>();

  let service: InvitationsV1Service;

  beforeAll(async () => {
    const module = await createTestingModule(InvitationsV1Module)
      .overrideProvider(InvitationsV1Repo)
      .useValue(repo)
      .overrideProvider(ConfigService)
      .useValue(config)
      .overrideProvider(DxService)
      .useValue(dx)
      .overrideProvider(EmailService)
      .useValue(email)
      .compile();

    service = module.get(InvitationsV1Service);
  });

  describe('postInvitation', () => {
    it('fails if no department found', async () => {
      repo.findDepartments.mockResolvedValue([]);

      const result = await service.postInvitation(
        [
          {
            email: '<EMAIL>',
            firstname: 'Test',
            lastname: 'User',
            departmentCode: 'D001',
            role: 'Admin',
          },
        ],
        { id: 1 } as any,
      );

      expect(
        errIs(result._unsafeUnwrapErr(), InvitationError.DepartmentNotFound),
      ).toEqual(true);
    });

    it('should send invitations and return Ok', async () => {
      const user = {
        id: 2,
        email: '<EMAIL>',
        firstname: 'John',
        lastname: 'Doe',
      } as Users;

      const invitation = {
        userId: user.id,
        identifier: 'token',
      } as Invitations;

      const department = {
        id: 1,
        code: 'D001',
        nameTh: 'Department 1',
        nameEn: 'Department 1',
      } as Departments;

      const role = {
        id: 1,
        name: typeof RoleName.AdminCentral,
      } as Roles;

      repo.findDepartments.mockResolvedValue([department]);
      repo.findOneUser.mockResolvedValue(null);
      repo.insertUser.mockResolvedValue(user);
      repo.findOneDepartment.mockResolvedValue(department);
      repo.findOneRole.mockResolvedValue(role);
      repo.insertInvitation.mockResolvedValue(invitation);
      email.send.mockResolvedValue(Ok(null));

      const result = await service.postInvitation(
        [
          {
            email: user.email,
            firstname: user.firstname,
            lastname: user.lastname,
            departmentCode: 'D001',
            role: 'Admin',
          },
        ],
        { id: 1, firstname: 'Admin', lastname: 'Root' } as any,
      );

      expect(result).toEqual(Ok(null));
      expect(email.send).toHaveBeenCalled();
    });
  });

  describe('postVerifyInvitation', () => {
    it('should return Err if invitation not found', async () => {
      repo.findOneInvitationByIdentifier.mockResolvedValue(null);

      const result = await service.postVerifyInvitation({ token: 'abc' });

      expect(
        errIs(result._unsafeUnwrapErr(), InvitationError.InvitationNotFound),
      ).toEqual(true);
    });

    it('should return Err if invitation expired', async () => {
      const expiredDate = tzDayjs().subtract(1, 'day').toDate();
      repo.findOneInvitationByIdentifier.mockResolvedValue({
        expiredAt: expiredDate,
        status: InviteStatus.Pending,
      } as Invitations);

      const result = await service.postVerifyInvitation({ token: 'abc' });

      expect(errIs(result._unsafeUnwrapErr(), InvitationError.Expired)).toEqual(
        true,
      );
    });

    it('should activate user and return Ok', async () => {
      const invitation = {
        userId: 2,
        expiredAt: tzDayjs().add(1, 'day').toDate(),
      } as Invitations;

      repo.findOneInvitationByIdentifier.mockResolvedValue(invitation);
      repo.findOneUser.mockResolvedValue({ id: 2 } as Users);

      const result = await service.postVerifyInvitation({ token: 'abc' });

      expect(result).toEqual(Ok(null));
      expect(repo.updateUser).toHaveBeenCalledWith(
        expect.objectContaining({ status: UserStatus.Active }),
      );
    });
  });
});
