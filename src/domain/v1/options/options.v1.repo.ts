import { Injectable } from '@nestjs/common';
import { FindManyOptions } from 'typeorm';

import { Disasters } from '@core/db/entities/disasters';
import { ResponsibilityAreas } from '@core/db/entities/responsibility-area';
import { BaseRepo } from '@core/shared/common/common.repo';

@Injectable()
export class OptionsV1Repo extends BaseRepo {
  async getAllDisasters(options?: FindManyOptions): Promise<Disasters[]> {
    return this.from(Disasters).find(options);
  }

  async getAllResponsibilityAreas(
    options?: FindManyOptions,
  ): Promise<ResponsibilityAreas[]> {
    return this.from(ResponsibilityAreas).find(options);
  }
}
