import { Controller, Get } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { ApiException } from '@core/shared/http/http.exception';

import { OptionsV1Service } from '../../options.v1.service';

@ApiTags('OptionsControllerV1')
@ApiBearerAuth()
@Controller({ path: 'options', version: '1' })
export class OptionsV1HttpController {
  constructor(private optionsV1Service: OptionsV1Service) {}

  @Get('departments')
  async getDepartments() {
    const r = await this.optionsV1Service.getDepartmentsOptions();

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data,
      }),
      (e) => {
        throw new ApiException(e, 500);
      },
    );
  }

  @Get('roles')
  async getRoles() {
    const r = await this.optionsV1Service.getRolesOptions();

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data,
      }),
      (e) => {
        throw new ApiException(e, 500);
      },
    );
  }

  @Get('disasters')
  async getDisasters() {
    const r = await this.optionsV1Service.getDisastersOptions();

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data,
      }),
      (e) => {
        throw new ApiException(e, 500);
      },
    );
  }

  @Get('responsibility-areas')
  async getResponsibilityAreas() {
    const r = await this.optionsV1Service.getResponsibilityAreasOptions();
    return r.match(
      (data) => ({
        success: true,
        key: '',
        data,
      }),
      (e) => {
        throw new ApiException(e, 500);
      },
    );
  }
}
