import { Injectable } from '@nestjs/common';

import { capitalizeFirstLetter } from '@core/shared/common/common.func';
import { Ok } from '@core/shared/common/common.neverthrow';

import { DepartmentsV1Repo } from '../departments/departments.v1.repo';
import { DisasterToTH } from '../disasters/disaster.v1.constant';
import { RoleName } from '../roles/roles.v1.constant';
import { RolesV1Repo } from '../roles/roles.v1.repo';
import { OptionsV1Repo } from './options.v1.repo';

@Injectable()
export class OptionsV1Service {
  constructor(
    private optionsRepo: OptionsV1Repo,
    private departmentsRepo: DepartmentsV1Repo,
    private roleRepo: RolesV1Repo,
  ) {}

  async getDepartmentsOptions() {
    const departments = await this.departmentsRepo.getAllDepartments({
      order: {
        nameTh: 'ASC',
      },
    });

    const returnData = departments.map((department) => ({
      label: department.nameTh,
      value: department.id,
    }));

    return Ok(returnData);
  }

  async getRolesOptions() {
    const roles = await this.roleRepo.getAllRoles();

    const itemOrders = [
      {
        role: RoleName.AdminCentral,
        label: 'ผู้ดูแลระบบหน่วยงานส่วนกลาง',
      },
      {
        role: RoleName.AdminProvincial,
        label: 'ผู้ดูแลระบบหน่วยงานส่วนจังหวัด',
      },
      {
        role: RoleName.UserCentralApprove,
        label: 'ผู้ใช้ส่วนกลาง (อนุมัติ)',
      },
      {
        role: RoleName.UserCentralCreate,
        label: 'ผู้ใช้ส่วนกลาง (สร้าง)',
      },
      {
        role: RoleName.UserProvincialApprove,
        label: 'ผู้ใช้ส่วนจังหวัด (อนุมัติ)',
      },
      {
        role: RoleName.UserProvincialCreate,
        label: 'ผู้ใช้ส่วนจังหวัด (สร้าง)',
      },
    ];

    const returnData = itemOrders.map((item) => {
      const role = roles.find((role) => role.name === item.role);
      return {
        label: item.label,
        value: role ? role.id : item.role,
      };
    });

    return Ok(returnData);
  }

  async getDisastersOptions() {
    const disaster = await this.optionsRepo.getAllDisasters({
      order: {
        id: 'ASC',
      },
    });

    const returnData = disaster.map((disaster) => ({
      label: DisasterToTH[capitalizeFirstLetter(disaster.name)],
      value: disaster.id,
    }));

    return Ok(returnData);
  }

  async getResponsibilityAreasOptions() {
    const responsibilityAreas =
      await this.optionsRepo.getAllResponsibilityAreas({
        order: {
          name: 'ASC',
        },
      });

    const returnData = responsibilityAreas.map((area) => ({
      label: area.name,
      shotName: area.shortName,
      value: area.id,
    }));

    return Ok(returnData);
  }
}
