import { Module } from '@nestjs/common';

import { DepartmentsV1Repo } from '../departments/departments.v1.repo';
import { RolesV1Repo } from '../roles/roles.v1.repo';
import { OptionsV1HttpController } from './handler/http/options.v1.http.controller';
import { OptionsV1Repo } from './options.v1.repo';
import { OptionsV1Service } from './options.v1.service';

@Module({
  providers: [OptionsV1Service, OptionsV1Repo, DepartmentsV1Repo, RolesV1Repo],
  controllers: [OptionsV1HttpController],
})
export class OptionsV1Module {}
