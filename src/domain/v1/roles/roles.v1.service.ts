import { Injectable } from '@nestjs/common';

import { ActivityLogs } from '@core/db/entities/activity-logs';
import { Users } from '@core/db/entities/users';
import { Err, Ok, Res } from '@core/shared/common/common.neverthrow';

import {
  ActivityLogAction,
  ActivityLogTargetType,
} from '@domain/v1/activity-logs/activity-logs.v1.constant';
import { RoleToTH } from '@domain/v1/roles/roles.v1.constant';

import { RolesV1Repo } from './roles.v1.repo';
import { RoleDetails } from './roles.v1.type';

@Injectable()
export class RolesV1Service {
  constructor(private repo: RolesV1Repo) {}

  async getRoleDetails(id: number): Promise<Res<RoleDetails, 'notFound'>> {
    const role = await this.repo.getOneRole(id);

    if (!role) {
      return Err('notFound');
    }

    return Ok({
      id: role.id,
      name: role.name,
    });
  }

  async patchUserRole(id: number, roleName: string, reqUser: Users) {
    const role = await this.repo.getOneRoleByName(roleName);

    if (!role) {
      return Err('notFound');
    }

    const user = await this.repo.getOneUser(id);
    if (!user) {
      return Err('notFound');
    }

    const userRole = user.userRoles.find((u) => u.role.id === role.id);
    if (!userRole) {
      return Err('notFound');
    }

    const oldRoleName = RoleToTH[userRole.role.name];
    const newRoleName = RoleToTH[role.name];

    const updateUser = {
      ...user,
      userRoles: user.userRoles.map((u) => {
        if (u.role.id === role.id) {
          return { ...u, role };
        }
        return u;
      }),
    };

    await this.repo.updateUser({ ...updateUser, forceSignOut: true });

    // TODO: add permission

    await this.repo.insertActivityLog({
      action: ActivityLogAction.Update,
      targetType: ActivityLogTargetType.User,
      targetId: user.id,
      description: `เปลี่ยนสิทธิ์ผู้ใช้ "${user.email}" จาก "${oldRoleName}" เป็น "${newRoleName}" เรียบร้อยแล้ว`,
      userId: reqUser.id,
    } as ActivityLogs);

    return Ok({
      id: user.id,
      email: user.email,
      role: { id: role.id, name: role.name },
    });
  }
}
