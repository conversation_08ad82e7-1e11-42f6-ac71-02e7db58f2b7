import { IStandardSingleApiResponse } from '@core/shared/http/http.standard';

import { DisasterReportDetails, ReportDetails } from '../../reports.v1.type';

class GetReportDetailsV1HttpData implements ReportDetails {
  id: number;
  name: string;
  status: string;
  actionType: string;
  documentType: string;
  data: any;
  backgroundColor: string;
  thumbnailUrl: string;
  layout: { id: number; direction: string };
  department: { id: number; nameTh: string };
  disasters?: DisasterReportDetails[];
  createdBy: {
    id: number;
    firstname: string;
    lastname: string;
    isMadeByMe: boolean;
  };
  reviewedBy?: {
    id?: number;
    firstname?: string;
    lastname?: string;
    isMadeByMe?: boolean;
  };
  latestRequestedBy?: {
    id?: number;
    firstname?: string;
    lastname?: string;
    isMadeByMe?: boolean;
  };
  latestEditedBy?: {
    id?: number;
    firstname?: string;
    lastname?: string;
    isMadeByMe?: boolean;
  };

  createdAt: Date;
  reviewedAt?: Date | null;
  latestRequestedAt?: Date | null;
  latestEditedAt?: Date | null;

  permission: { canCreate: boolean; canEdit: boolean };
}

export class GetReportDetailsV1HttpResponse
  implements IStandardSingleApiResponse
{
  success: boolean;
  key: string;
  data: GetReportDetailsV1HttpData;
}
