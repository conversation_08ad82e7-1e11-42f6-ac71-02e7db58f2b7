import { Controller, Get, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { Users } from '@core/db/entities/users';
import { ReqUser } from '@core/middleware/jwt/jwt.common';
import { ApiException } from '@core/shared/http/http.exception';

import { ReportsV1Service } from '../reports.v1.service';
import { GetReportDetailsV1HttpResponse } from './dto/get-report-details.v1.http.dto';
import {
  GetReportsV1HttpParam,
  GetReportsV1HttpResponse,
} from './dto/get-reports.v1.http.dto';

@ApiTags('ReportsControllerV1')
@ApiBearerAuth()
@Controller({
  path: 'reports',
  version: '1',
})
export class ReportsV1HttpController {
  constructor(private reportService: ReportsV1Service) {}

  @Get()
  async getReports(
    @Query() options: GetReportsV1HttpParam,
    @ReqUser() user: Users,
  ): Promise<GetReportsV1HttpResponse> {
    const r = await this.reportService.getPaginateReports(user, options);

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data: data.datas,
        meta: {
          pagination: data.pagination,
          totalApproved: data.totalApproved,
          totalPending: data.totalPending,
          totalReports: data.totalReports,
        },
      }),
      (e) => {
        throw new ApiException(e, 500);
      },
    );
  }

  @Get(':id')
  async getReportDetail(
    @Query('id') id: number,
    @ReqUser() user: Users,
  ): Promise<GetReportDetailsV1HttpResponse> {
    const r = await this.reportService.getReportDetailsById(id, user);

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data: data.datas,
      }),
      (e) => {
        throw new ApiException(e, 500);
      },
    );
  }
}
