import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';

import { transformToNumberArray } from '@core/shared/common/common.func';
import { PaginationOptions } from '@core/shared/common/common.pagintaion';
import {
  TypeOrmOrderBy,
  TypeOrmOrderByType,
} from '@core/shared/common/common.typeorm';
import { PaginationResponseSchema } from '@core/shared/http/http.response.dto';
import { IStandardArrayApiResponse } from '@core/shared/http/http.standard';

import { UserPaginateDetails } from '@domain/v1/users/users.v1.type';

import { UserStatus } from '../../../users.v1.constant';

// === request ===

export class GetUsersV1HttpParam implements PaginationOptions {
  @ApiProperty({
    example: 1,
    type: Number,
  })
  @IsNotEmpty()
  @Type(() => Number)
  page: number;

  @ApiProperty({
    example: 10,
    type: Number,
  })
  @IsNotEmpty()
  @Type(() => Number)
  perPage: number;

  @ApiPropertyOptional({
    enum: [UserStatus.Active, UserStatus.Invited],
    example: UserStatus.Active,
  })
  @IsOptional()
  @IsEnum([UserStatus.Active, UserStatus.Invited])
  status?: string;

  @ApiPropertyOptional({
    type: String,
    example: '[1,2,3]',
    description: 'Role IDs in string format, will be converted to number array',
  })
  @IsOptional()
  @Transform(({ value }) => transformToNumberArray(value))
  roleId?: number[];

  @ApiPropertyOptional({
    type: String,
    example: '[1,2,3]',
    description:
      'Department IDs in string format, will be converted to number array',
  })
  @IsOptional()
  @Transform(({ value }) => transformToNumberArray(value))
  departmentId?: number[];

  @ApiPropertyOptional({
    type: String,
    example: 'john',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    enum: ['fullname', 'lastSignedInAt', 'createdAt', `departmentNameTh`],
    default: 'fullname',
    example: 'fullname',
  })
  @IsOptional()
  @IsEnum(['fullname', 'lastSignedInAt', 'createdAt', `departmentNameTh`])
  sortBy?: string;

  @ApiPropertyOptional({
    enum: TypeOrmOrderBy,
    default: TypeOrmOrderBy.ASC,
    example: TypeOrmOrderBy.ASC,
  })
  @IsOptional()
  @IsEnum(TypeOrmOrderBy)
  orderBy?: TypeOrmOrderByType;
}

// === response ===

class GetUsersV1HttpData implements UserPaginateDetails {
  id: number;
  email: string;
  firstname: string;
  lastname: string;
  fullname: string;
  status: string;
  lastSignedInAt: Date | null;
  createdAt: Date;
  roleName?: string;
  departmentNameTh?: string;
  departmentNameEn?: string;
  departmentCode?: string;
  totalTemplates: number;
  totalReports: number;
}

export class UsersMetaResponse {
  pagination: PaginationResponseSchema;
  totalInvited: number;
  totalActive: number;
}

export class GetUsersV1HttpResponse
  implements IStandardArrayApiResponse<UsersMetaResponse>
{
  success: boolean;
  key: string;
  data: GetUsersV1HttpData[];
  meta: UsersMetaResponse;
}
