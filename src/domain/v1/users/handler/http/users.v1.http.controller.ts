import { Body, Controller, Delete, Get, Param, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { Users } from '@core/db/entities/users';
import { DxService } from '@core/global/dx/dx.service';
import { ReqUser } from '@core/middleware/jwt/jwt.common';
import { errIs } from '@core/shared/common/common.neverthrow';
import { ApiException } from '@core/shared/http/http.exception';

import { UsersV1Service } from '../../users.v1.service';
import { DeleteUsersV1HttpDto } from './dto/delete-users.v1.http.dto';
import { GetInviteUserDetailsV1HttpResponse } from './dto/get-invite-user-details.v1.dto';
import { GetProfileV1HttpResponse } from './dto/get-profile.v1.dto';
import {
  GetUsersV1HttpParam,
  GetUsersV1HttpResponse,
} from './dto/get-users.v1.http.dto';

@ApiTags('UserControllerV1')
@ApiBearerAuth()
@Controller({
  path: 'users',
  version: '1',
})
export class UsersV1HttpController {
  constructor(
    private service: UsersV1Service,
    private dxService: DxService,
  ) {}

  @Get()
  async getUsers(
    @Query() options: GetUsersV1HttpParam,
  ): Promise<GetUsersV1HttpResponse> {
    const r = await this.service.getPaginateUsers(options);

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data: data.datas,
        meta: {
          pagination: data.pagination,
          totalInvited: data.totalInvited,
          totalActive: data.totalActive,
        },
      }),
      (e) => {
        throw new ApiException(e, 500);
      },
    );
  }

  @Get('profile')
  async getProfile(
    @ReqUser() reqUser: Users,
  ): Promise<GetProfileV1HttpResponse> {
    const r = await this.service.getProfile(reqUser);

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data,
      }),
      (e) => {
        if (errIs(e, 'notFound')) throw new ApiException(e, 400);
        throw new ApiException(e, 500);
      },
    );
  }

  @Get('invite/:citizenId')
  async getInviteUserDetails(
    @Param('citizenId') citizenId: string,
  ): Promise<GetInviteUserDetailsV1HttpResponse> {
    const r = await this.service.getInviteUserDetails(citizenId);

    return r.match(
      (data) => ({ success: true, key: '', data }),
      (e) => {
        if (errIs(e, 'notFound')) {
          throw new ApiException(e, 400);
        }

        throw new ApiException(e, 500);
      },
    );
  }

  @Delete()
  async DeleteUsers(
    @Body() deleteUsersV1HttpDto: DeleteUsersV1HttpDto,
  ): Promise<GetInviteUserDetailsV1HttpResponse> {
    const r = await this.service.deleteUsers(deleteUsersV1HttpDto.userIds);

    return r.match(
      (data) => ({ success: true, key: '', data }),
      (e) => {
        if (errIs(e, 'lastCentralAdmin')) {
          throw new ApiException(e, 422);
        }
        if (errIs(e, 'notFound')) {
          throw new ApiException(e, 404);
        }
        throw new ApiException(e, 500);
      },
    );
  }
}
