import { Injectable } from '@nestjs/common';
import {
  Brackets,
  FindOneOptions,
  FindOptionsRelations,
  FindOptionsWhere,
} from 'typeorm';

import { Departments } from '@core/db/entities/departments';
import { UserRoles } from '@core/db/entities/user-roles';
import { Users } from '@core/db/entities/users';
import {
  PaginationOptions,
  getLimit,
  getOffset,
  getPagination,
} from '@core/shared/common/common.pagintaion';
import { BaseRepo } from '@core/shared/common/common.repo';
import { TypeOrmOrderByType } from '@core/shared/common/common.typeorm';

import { UserStatus } from './users.v1.constant';
import { NewUser } from './users.v1.type';

@Injectable()
export class UsersV1Repo extends BaseRepo {
  async getOneUser(query: {
    where?: FindOptionsWhere<Users>;
    relations?: FindOptionsRelations<Users>;
  }): Promise<Users | null> {
    return this.from(Users).findOne({
      where: query.where,
      relations: query.relations,
    });
  }

  async getUsers(query: {
    where?: FindOptionsWhere<Users>;
    relations?: FindOptionsRelations<Users>;
  }): Promise<Users[] | []> {
    return this.from(Users).find({
      where: query.where,
      relations: query.relations,
    });
  }

  async existedUser(where: FindOptionsWhere<Users>): Promise<boolean> {
    return this.from(Users).exists({ where });
  }

  async getPaginateUsers(
    paginatedOptions: PaginationOptions,
    query: {
      status?: string;
      roleId?: number[];
      departmentId?: number[];
      search?: string;
      orderBy: TypeOrmOrderByType;
      sortBy: string;
    },
  ) {
    // Create subquery to get the first role for each user
    const userRoleSubQuery = this.from(UserRoles)
      .createQueryBuilder('ur')
      .select(['ur.user_id', 'ur.role_id'])
      .distinctOn(['ur.user_id'])
      .orderBy('ur.user_id')
      .addOrderBy('ur.Id', 'ASC');

    const qb = this.from(Users)
      .createQueryBuilder('user')
      .leftJoin(
        `(${userRoleSubQuery.getQuery()})`,
        'ur',
        'ur.user_id = user.id',
      )
      .leftJoin('roles', 'role', 'role.id = ur.role_id')
      .leftJoin('user.department', 'department')
      .select([
        'user.id AS id',
        'user.email AS email',
        'user.firstname AS firstname',
        'user.lastname AS lastname',
        'user.status AS status',
        'user.lastSignedInAt AS last_signed_in_at',
        'user.createdAt AS created_at',
        'role.name AS role_name',
        'department.nameTh AS department_name_th',
        'department.nameEn AS department_name_en',
        'department.code AS department_code',
      ]);

    if (query.status)
      qb.andWhere('user.status = :status', { status: query.status });

    if (query.roleId) {
      qb.andWhere('role.id IN (:...roleIds)', {
        roleIds: query.roleId,
      });
    }

    if (query.departmentId) {
      qb.andWhere(`department.id IN (:...departmentIds)`, {
        departmentIds: query.departmentId,
      });
    }

    if (query.search) {
      const formattedSearch = query.search.trim().toLowerCase();

      qb.andWhere(
        new Brackets((qb) => {
          qb.where('user.email ILIKE :search', {
            search: `%${formattedSearch}%`,
          });

          qb.orWhere(
            "CONCAT(user.firstname, ' ', user.lastname) ILIKE :search",
            { search: `%${formattedSearch}%` },
          );
        }),
      );
    }

    if (query.sortBy === 'fullname') {
      qb.orderBy(
        `CONCAT(COALESCE(user.firstname, ''), ' ', COALESCE(user.lastname, ''))`,
        query.orderBy,
      );
    } else if (query.sortBy === 'lastSignedInAt') {
      qb.orderBy('user.lastSignedInAt', query.orderBy);
    } else if (query.sortBy === 'createdAt') {
      qb.orderBy('user.createdAt', query.orderBy);
    } else if (query.sortBy === 'departmentNameTh') {
      qb.orderBy('department.nameTh', query.orderBy);
    }

    const datas = await qb
      .take(getLimit(paginatedOptions))
      .skip(getOffset(paginatedOptions))
      .getRawMany();

    const totalItems = await qb.getCount();

    return {
      datas: datas.map((data) => ({
        id: data.id,
        email: data.email,
        firstname: data.firstname,
        lastname: data.lastname,
        fullname: data.firstname ? `${data.firstname} ${data.lastname}` : '',
        status: data.status,
        lastSignedInAt: data.last_signed_in_at || null,
        createdAt: data.created_at,
        roleName: data?.role_name || '',
        departmentNameTh: data?.department_name_th || '',
        departmentNameEn: data?.department_name_en || '',
        departmentCode: data?.department_code || '',
        totalTemplates: 0,
        totalReports: 0,
      })),
      pagination: getPagination(datas, totalItems, paginatedOptions),
    };
  }

  async getActiveUserCounts(): Promise<number> {
    return await this.from(Users).countBy({ status: UserStatus.Active });
  }

  async getInvitedUserCounts(): Promise<number> {
    return await this.from(Users).countBy({ status: UserStatus.Invited });
  }

  async insertUser(data: NewUser): Promise<Users> {
    const user = this.from(Users).create(data);
    await this.from(Users).insert(user);
    return user;
  }

  async updateUser(user: Users): Promise<void> {
    const { id, ...data } = user;

    await this.from(Users).update(id, data);
  }

  async softDeleteUsers(userIds: number[]) {
    return await this.from(Users).softDelete(userIds);
  }

  async getOneDepartment(
    options: FindOneOptions<Departments>,
  ): Promise<Departments | null> {
    return this.from(Departments).findOne(options);
  }
}
