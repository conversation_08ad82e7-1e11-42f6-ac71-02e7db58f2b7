import { Dayjs } from 'dayjs';
import { mock } from 'jest-mock-extended';
import { In } from 'typeorm';

import { Departments } from '@core/db/entities/departments';
import { RolePermissions } from '@core/db/entities/role-permissions';
import { Roles } from '@core/db/entities/roles';
import { UserRoles } from '@core/db/entities/user-roles';
import { Users } from '@core/db/entities/users';
import { CacheService } from '@core/global/cache/cache.service';
import { DxService } from '@core/global/dx/dx.service';
import tzDayjs from '@core/shared/common/common.dayjs';
import { Err, Ok } from '@core/shared/common/common.neverthrow';
import { TypeOrmOrderBy } from '@core/shared/common/common.typeorm';
import {
  createTestingModule,
  freezeTestTime,
} from '@core/test/test-util/test-util.common';

import {
  PermissionsItem,
  PermissionsType,
} from '@domain/v1/permissions/permissions.v1.constant';
import { RoleName } from '@domain/v1/roles/roles.v1.constant';

import { UserError, UserStatus } from '../users.v1.constant';
import { UsersV1Module } from '../users.v1.module';
import { UsersV1Repo } from '../users.v1.repo';
import { UsersV1Service } from '../users.v1.service';

describe('UsersV1Service', () => {
  const repo = mock<UsersV1Repo>();
  const dx = mock<DxService>();
  const cache = mock<CacheService>();
  let service: UsersV1Service;
  let current: Dayjs;

  beforeAll(async () => {
    const module = await createTestingModule(UsersV1Module)
      .overrideProvider(UsersV1Repo)
      .useValue(repo)
      .overrideProvider(DxService)
      .useValue(dx)
      .overrideProvider(CacheService)
      .useValue(cache)
      .compile();

    service = module.get(UsersV1Service);
  });

  describe('getPaginateUsers', () => {
    current = tzDayjs();
    freezeTestTime(current);

    it('works', async () => {
      const mockDatas = [
        {
          id: 1,
          email: '<EMAIL>',
          firstname: 'test',
          lastname: 'test',
          fullname: '',
          status: 'active',
          lastSignedInAt: null,
          createdAt: '2025-05-30T11:52:57.983Z',
          roleName: 'superadmin',
          departmentCode: 2,
          departmentNameTh:
            'ศูนย์ป้องกันและบรรเทาสาธารณภัย เขต 4 ประจวบคีรีขันธ์',
          departmentNameEn:
            'Disaster Prevention and Mitigation Regional Center 4 Prachuapkhirikhan',
          totalTemplates: 0,
          totalReports: 0,
        },
      ];
      const mockPaginate = {
        page: 1,
        nextPage: 1,
        previousPage: 1,
        perPage: 10,
        totalPages: 1,
        currentPageItems: 4,
        totalItems: 4,
      };

      repo.getPaginateUsers.mockResolvedValue({
        datas: mockDatas,
        pagination: mockPaginate,
      });
      repo.getActiveUserCounts.mockResolvedValue(10);
      repo.getInvitedUserCounts.mockResolvedValue(100);

      const result = await service.getPaginateUsers({
        status: 'active',
        roleId: [1],
        departmentId: [2],
        search: 'test',
        orderBy: TypeOrmOrderBy.ASC,
        sortBy: 'fullname',
        page: 1,
        perPage: 10,
      });

      expect(repo.getPaginateUsers).toHaveBeenCalledWith(
        { page: 1, perPage: 10 },
        {
          status: 'active',
          roleId: [1],
          departmentId: [2],
          search: 'test',
          orderBy: TypeOrmOrderBy.ASC,
          sortBy: 'fullname',
        },
      );
      expect(repo.getActiveUserCounts).toHaveBeenCalled();
      expect(repo.getInvitedUserCounts).toHaveBeenCalled();
      expect((result as any).value).toEqual({
        datas: mockDatas,
        pagination: mockPaginate,
        totalActive: 10,
        totalInvited: 100,
      });
    });
  });

  describe('getProfile', () => {
    const mockUser = { id: 1 } as Users;

    const profileData = {
      id: 1,
      email: '<EMAIL>',
      firstname: 'Test',
      lastname: 'User',
      username: 'testuser',
      role: { id: 1, name: 'admin' },
      permissions: [
        {
          id: 1,
          type: PermissionsType.Read,
          item: PermissionsItem.User,
          approveStatus: null,
          department: null,
          area: null,
        },
        {
          id: 1,
          type: PermissionsType.Edit,
          item: PermissionsItem.User,
          approveStatus: null,
          department: null,
          area: null,
        },
      ],
      department: {
        id: 1,
        nameTh: 'ชื่อไทย',
        nameEn: 'English Name',
        code: 'DPT001',
      },
    };

    it('returns cached profile if exists', async () => {
      cache.get.mockResolvedValue(Ok(profileData));

      const result = await service.getProfile(mockUser);

      expect(cache.get).toHaveBeenCalledWith('profile:1');
      expect(repo.getOneUser).not.toHaveBeenCalled();

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual(profileData);
    });

    it('returns profile from DB and caches it if not in cache', async () => {
      cache.get.mockResolvedValue(Ok(null));

      const userRoles = [
        {
          role: {
            id: 1,
            name: 'admin',
            rolePermissions: [
              {
                permission: {
                  id: 1,
                  type: PermissionsType.Read,
                  item: PermissionsItem.User,
                  approveStatus: null,
                  department: null,
                  area: null,
                },
              },
              {
                permission: {
                  id: 1,
                  type: PermissionsType.Edit,
                  item: PermissionsItem.User,
                  approveStatus: null,
                  department: null,
                  area: null,
                },
              },
            ] as RolePermissions[],
          } as Roles,
        },
      ] as UserRoles[];

      repo.getOneUser.mockResolvedValue({
        id: 1,
        email: '<EMAIL>',
        firstname: 'Test',
        lastname: 'User',
        username: 'testuser',
        userRoles,
        department: {
          id: 1,
          nameTh: 'ชื่อไทย',
          nameEn: 'English Name',
          code: 'DPT001',
        } as Departments,
      } as Users);

      const result = await service.getProfile(mockUser);

      expect(cache.get).toHaveBeenCalledWith('profile:1');
      expect(repo.getOneUser).toHaveBeenCalledWith({
        where: { id: mockUser.id },
        relations: {
          department: true,
          userRoles: {
            role: { rolePermissions: { permission: true } },
          },
        },
      });

      expect(cache.set).toHaveBeenCalledWith(
        'profile:1',
        profileData,
        expect.any(Number),
      );
      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual(profileData);
    });

    it('returns Err(notFound) if user not found in DB', async () => {
      cache.get.mockResolvedValue(Ok(null));
      repo.getOneUser.mockResolvedValue(null);

      const result = await service.getProfile(mockUser);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().key).toBe('notFound');
    });
  });

  describe('getInviteUserDetails', () => {
    const citizenId = '1234567890123';

    const dxUserMock = {
      citizenId,
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      departmentID: 'DPT001',
    };

    const departmentMock = {
      id: 1,
      nameTh: 'ฝ่ายทดสอบ',
      nameEn: 'Test Department',
      code: 'DPT001',
      responsibilityArea: {
        id: 99,
        name: 'พื้นที่รับผิดชอบ',
      },
    };

    it('returns Err(notFound) if dxService fails', async () => {
      dx.getUserInfo.mockResolvedValue(Err('some-error'));

      const result = await service.getInviteUserDetails(citizenId);

      expect(dx.getUserInfo).toHaveBeenCalledWith(citizenId);
      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().key).toBe(UserError.NotFounded);
    });

    it('returns Err(notFound) if dxUser lacks departmentID or email', async () => {
      dx.getUserInfo.mockResolvedValue(
        Ok({ ...dxUserMock, departmentID: '', email: '' } as any),
      );

      const result = await service.getInviteUserDetails(citizenId);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().key).toBe('notFound');
    });

    it('returns profile using existedUser.department if user exists', async () => {
      dx.getUserInfo.mockResolvedValue(Ok(dxUserMock as any));
      repo.getOneUser.mockResolvedValue({
        status: UserStatus.Invited,
        department: departmentMock as Departments,
      } as Users);
      repo.getOneDepartment.mockReset();

      const result = await service.getInviteUserDetails(citizenId);

      expect(repo.getOneUser).toHaveBeenCalledWith({
        where: { email: dxUserMock.email },
        relations: { department: { responsibilityArea: true } },
      });

      expect(repo.getOneDepartment).not.toHaveBeenCalled();
      expect(result.isOk()).toBe(true);

      expect(result._unsafeUnwrap()).toEqual({
        id: dxUserMock.citizenId,
        firstname: dxUserMock.firstName,
        lastname: dxUserMock.lastName,
        email: dxUserMock.email,
        status: UserStatus.Invited,
        department: {
          id: departmentMock.id,
          nameTh: departmentMock.nameTh,
          nameEn: departmentMock.nameEn,
          code: departmentMock.code,
          responsibilityArea: {
            id: departmentMock.responsibilityArea.id,
            name: departmentMock.responsibilityArea.name,
          },
        },
      });
    });

    it('returns profile using department from repo if user not found', async () => {
      dx.getUserInfo.mockResolvedValue(Ok(dxUserMock as any));
      repo.getOneUser.mockResolvedValue(null);
      repo.getOneDepartment.mockResolvedValue(departmentMock as any);

      const result = await service.getInviteUserDetails(citizenId);

      expect(repo.getOneUser).toHaveBeenCalled();
      expect(repo.getOneDepartment).toHaveBeenCalledWith({
        where: { code: dxUserMock.departmentID },
        relations: { responsibilityArea: true },
      });

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual({
        id: dxUserMock.citizenId,
        firstname: dxUserMock.firstName,
        lastname: dxUserMock.lastName,
        email: dxUserMock.email,
        status: undefined,
        department: {
          id: departmentMock.id,
          nameTh: departmentMock.nameTh,
          nameEn: departmentMock.nameEn,
          code: departmentMock.code,
          responsibilityArea: {
            id: departmentMock.responsibilityArea.id,
            name: departmentMock.responsibilityArea.name,
          },
        },
      });
    });
  });

  describe('deleteUsers', () => {
    const mockCentralAdmins = [{ id: 1 }, { id: 2 }] as Users[];

    it('returns error if there are 1 cnetral admins left', async () => {
      repo.getUsers.mockResolvedValue([{ id: 1 }] as Users[]);

      const result = await service.deleteUsers([1, 2]);

      expect(repo.getUsers).toHaveBeenCalledWith({
        where: {
          userRoles: {
            role: { name: RoleName.AdminCentral },
          },
        },
      });
      expect(result._unsafeUnwrapErr().key).toBe('lastCentralAdmin');
    });

    it('returns error if user list has superadmin', async () => {
      repo.getUsers.mockResolvedValue(mockCentralAdmins);
      repo.existedUser.mockResolvedValue(true);

      const result = await service.deleteUsers([1]);

      expect(repo.getUsers).toHaveBeenCalledWith({
        where: {
          userRoles: {
            role: { name: RoleName.AdminCentral },
          },
        },
      });

      expect(repo.existedUser).toHaveBeenCalledWith({
        id: In([1]),
        userRoles: {
          role: { name: RoleName.SuperAdmin },
        },
      });
      expect(result._unsafeUnwrapErr().key).toBe('hasSuperAdmin');
    });

    it('works', async () => {
      repo.getUsers.mockResolvedValueOnce(mockCentralAdmins);
      repo.existedUser.mockResolvedValue(false);
      repo.getUsers.mockResolvedValueOnce([{ id: 1 }] as Users[]);

      const result = await service.deleteUsers([1]);

      expect(result.isOk()).toBeTruthy();
    });
  });
});
