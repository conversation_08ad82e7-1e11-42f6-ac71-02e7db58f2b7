import { Injectable } from '@nestjs/common';
import { In } from 'typeorm';

import { Users } from '@core/db/entities/users';
import { ONE_HOUR_CACHE } from '@core/global/cache/cache.constant';
import { CacheService } from '@core/global/cache/cache.service';
import { DxService } from '@core/global/dx/dx.service';
import { Err, Ok, Res } from '@core/shared/common/common.neverthrow';
import { TypeOrmOrderBy } from '@core/shared/common/common.typeorm';
import { IPaginationSchema } from '@core/shared/http/http.standard';

import { GetUsersV1HttpParam } from '@domain/v1/users/handler/http/dto/get-users.v1.http.dto';

import { RoleName } from '../roles/roles.v1.constant';
import { UsersV1Repo } from './users.v1.repo';
import { UserInviteDetails, UserPaginateDetails } from './users.v1.type';

@Injectable()
export class UsersV1Service {
  constructor(
    private userRepo: UsersV1Repo,
    private dxService: DxService,
    private cacheService: CacheService,
  ) {}

  async getPaginateUsers(
    options: GetUsersV1HttpParam,
  ): Promise<Res<GetPaginateUsers, ''>> {
    const query = {
      status: options.status,
      roleId: options.roleId,
      departmentId: options.departmentId,
      search: options.search,
      orderBy: options.orderBy || TypeOrmOrderBy.ASC,
      sortBy: options.sortBy || 'fullname',
    };

    const { datas, pagination } = await this.userRepo.getPaginateUsers(
      { page: options.page, perPage: options.perPage },
      query,
    );

    const totalInvited = await this.userRepo.getInvitedUserCounts();
    const totalActive = await this.userRepo.getActiveUserCounts();

    return Ok({ datas, pagination, totalActive, totalInvited });
  }

  async getProfile(reqUser: Users): Promise<Res<GetProfile, 'notFound'>> {
    const cacheKey = `profile:${reqUser.id}`;

    const cacheResult = await this.cacheService.get(cacheKey);

    const userProfile = await cacheResult.match(
      async (cachedProfile) => {
        if (cachedProfile) return cachedProfile as GetProfile;

        const foundedUser = await this.userRepo.getOneUser({
          where: { id: reqUser.id },
          relations: {
            department: true,
            userRoles: {
              role: { rolePermissions: { permission: true } },
            },
          },
        });

        if (!foundedUser) return null;

        const profile = {
          id: foundedUser.id,
          email: foundedUser.email,
          firstname: foundedUser.firstname,
          lastname: foundedUser.lastname,
          username: foundedUser.username,
          role: {
            id: foundedUser.userRoles[0].role.id,
            name: foundedUser.userRoles[0].role.name,
          },
          department: {
            id: foundedUser.department.id,
            nameTh: foundedUser.department.nameTh,
            nameEn: foundedUser.department.nameEn,
            code: foundedUser.department.code,
          },
          permissions: foundedUser.userRoles[0].role.rolePermissions.map(
            (rp) => ({
              id: rp.permission.id,
              type: rp.permission.type,
              item: rp.permission.item,
              approveStatus: rp.permission.approveStatus,
              department: rp.permission.department,
              area: rp.permission.area,
              description: rp.permission.description,
            }),
          ),
        };

        await this.cacheService.set(cacheKey, profile, ONE_HOUR_CACHE);

        return profile;
      },
      () => {
        return null;
      },
    );

    if (!userProfile) return Err('notFound');

    return Ok(userProfile);
  }

  async getInviteUserDetails(
    _citizenId: string,
  ): Promise<Res<UserInviteDetails, 'notFound'>> {
    const dxUser = await this.dxService.getUserInfo(_citizenId);

    if (dxUser.isErr()) return Err('notFound');

    const { departmentID, email, citizenId, firstName, lastName } =
      dxUser.value;

    if (!departmentID || !email) return Err('notFound');

    const existedUser = await this.userRepo.getOneUser({
      where: { email },
      relations: { department: { responsibilityArea: true } },
    });

    const department =
      existedUser?.department ||
      (await this.userRepo.getOneDepartment({
        where: { code: departmentID },
        relations: { responsibilityArea: true },
      }));

    return Ok({
      id: citizenId,
      firstname: firstName,
      lastname: lastName,
      email,
      status: existedUser?.status,
      ...(department && {
        department: {
          id: department.id!,
          nameTh: department.nameTh,
          nameEn: department.nameEn,
          code: department.code,
          responsibilityArea: {
            id: department.responsibilityArea.id,
            name: department.responsibilityArea.name,
          },
        },
      }),
    });
  }

  async deleteUsers(
    userIds: number[],
  ): Promise<Res<Users[], 'lastCentralAdmin' | 'hasSuperAdmin' | 'notFound'>> {
    const centralAdmins = await this.userRepo.getUsers({
      where: {
        userRoles: {
          role: { name: RoleName.AdminCentral },
        },
      },
    });

    const remainingCentralAdmin = centralAdmins.filter(
      (user) => !userIds.includes(user.id),
    ).length;
    if (remainingCentralAdmin < 1) return Err('lastCentralAdmin');

    const hasSuperAdmin = await this.userRepo.existedUser({
      id: In(userIds),
      userRoles: {
        role: { name: RoleName.SuperAdmin },
      },
    });

    if (hasSuperAdmin) return Err('hasSuperAdmin');

    const deleteUsers = await this.userRepo.getUsers({
      where: { id: In(userIds) },
    });

    if (deleteUsers.length !== userIds.length) return Err('notFound');

    await this.userRepo.softDeleteUsers(userIds);

    return Ok(deleteUsers);
  }
}

// ============= type ==========

type GetPaginateUsers = {
  datas: UserPaginateDetails[];
  pagination: IPaginationSchema;
  totalInvited: number;
  totalActive: number;
};

type GetProfile = {
  id: number;
  email: string;
  firstname: string;
  lastname: string;
  username: string;
  two_fa_sent_at?: Date;
  role: {
    id: number;
    name: string;
  };
  permissions: {
    id: number;
    type: string;
    item: string;
    approveStatus: string | null;
    department: string | null;
    area: string | null;
  }[];
  department: {
    id: number;
    nameTh: string;
    nameEn: string;
    code: string;
  };
};
