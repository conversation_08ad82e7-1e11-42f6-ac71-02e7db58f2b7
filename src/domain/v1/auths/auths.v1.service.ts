import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { authenticator, hotp } from 'otplib';

import { AppConfig } from '@core/config';
import { Users } from '@core/db/entities/users';
import { DxError } from '@core/global/dx/dx.constant';
import { DxService } from '@core/global/dx/dx.service';
import { emailTemplate } from '@core/global/email/email.contants';
import { EmailService } from '@core/global/email/email.service';
import {
  encodeUserJwt,
  isMatchedHash,
} from '@core/shared/common/common.crypto';
import tzDayjs from '@core/shared/common/common.dayjs';
import { Err, Ok, Res } from '@core/shared/common/common.neverthrow';

import { RoleName } from '../roles/roles.v1.constant';
import { UserStatus } from '../users/users.v1.constant';
import { AuthError } from './auth.constant';
import { AuthsV1Repo } from './auths.v1.repo';
import {
  AuthDetails,
  OtpDetails,
  OtpUserData,
  PostAuthSignInError,
  PostOtpError,
  PostRefreshTokenError,
  RefreshTokenUserData,
  RefreshtokenDetails,
  SignInUserData,
} from './auths.v1.type';

@Injectable()
export class AuthsV1Service {
  constructor(
    private repo: AuthsV1Repo,
    private configService: ConfigService,
    private dxService: DxService,
    private emailService: EmailService,
  ) {}

  async postAuthsSignIns(
    data: SignInUserData,
  ): Promise<Res<AuthDetails, PostAuthSignInError>> {
    const user = await this.repo.getOneUser(data.email);

    if (!user) return Err(AuthError.NotInvited);
    if (user.status !== UserStatus.Active) return Err(AuthError.InActive);

    const authenticateResult = await this._authenticateUser(
      user,
      data.password,
    );

    if (authenticateResult.isErr()) return Err(authenticateResult.error.key);

    const now = tzDayjs().toDate();
    user.lastSignedInAt = now;
    user.updatedAt = now;

    if (user.userRoles.some((u) => u.role.name === RoleName.SuperAdmin)) {
      // If user is SuperAdmin, skip 2FA
      const { accessToken, refreshToken } = this._generateToken(user);

      user.refreshToken = refreshToken;
      await this.repo.updateUser(user);

      return Ok({
        accessToken,
        refreshToken,
        lastSignedInAt: user.lastSignedInAt,
      });
    }

    const twoFaSecret = user.twoFaSecret || authenticator.generateSecret();
    const newTwoFaCounter = user.twoFaCounter + 1;
    const token = hotp.generate(twoFaSecret, newTwoFaCounter);

    const sendResult = await this.emailService.send({
      templateType: emailTemplate.OTP,
      recipient: {
        email: user.email,
        firstname: user.firstname,
        lastname: user.lastname,
        token,
      },
      subject: 'ส่ง OTP',
    });

    if (sendResult.isErr()) return Err(AuthError.InvalidOtp);

    user.twoFaSecret = twoFaSecret;
    user.twoFaCounter = newTwoFaCounter;
    user.twoFaSentAt = now;

    await this.repo.updateUser(user);

    return Ok({
      lastSignedInAt: user.lastSignedInAt,
    });
  }

  async postOtp(data: OtpUserData): Promise<Res<OtpDetails, PostOtpError>> {
    const user = await this.repo.getOneUser(data.email);

    if (!user) return Err(AuthError.NotInvited);
    if (user.status !== UserStatus.Active) return Err(AuthError.InActive);

    const authenticateResult = await this._authenticateUser(
      user,
      data.password,
    );

    if (authenticateResult.isErr()) return Err(authenticateResult.error.key);

    const isOtpValid = hotp.check(
      data.otp,
      user.twoFaSecret,
      user.twoFaCounter,
    );
    const otpExpired = tzDayjs().isAfter(
      tzDayjs(user.twoFaSentAt).add(10, 'minutes'),
    );

    if (!isOtpValid || otpExpired) return Err(AuthError.InvalidOtp);

    const { accessToken, refreshToken } = this._generateToken(user);

    user.refreshToken = refreshToken;
    await this.repo.updateUser(user);

    return Ok({
      accessToken,
      refreshToken,
    });
  }

  async postRegreshToken(
    data: RefreshTokenUserData,
  ): Promise<Res<RefreshtokenDetails, PostRefreshTokenError>> {
    const user = await this.repo.getUserByRefreshtoken(data.refreshToken);

    if (!user) return Err(AuthError.InvalidToken);

    const { accessToken } = this._generateToken(user);

    return Ok({
      accessToken,
    });
  }

  // Logic Helper ========
  private async _authenticateUser(
    user: Users,
    rawPassword: string,
  ): Promise<
    Res<Users, typeof DxError.DxError | typeof AuthError.InvalidEmailOrPassword>
  > {
    const env = this.configService.getOrThrow<AppConfig['app']>('app').nodeEnv;

    if (
      user.userRoles.some((u) => u.role.name === RoleName.SuperAdmin) ||
      env === 'develop'
    ) {
      if (!isMatchedHash(rawPassword, user.password))
        return Err(AuthError.InvalidEmailOrPassword);
    } else {
      const dxLoginResponse = await this.dxService.login(
        user.email,
        rawPassword,
      );

      if (dxLoginResponse.isErr()) return Err(DxError.DxError);
      if (dxLoginResponse.value.result.result !== 'ok')
        return Err(AuthError.InvalidEmailOrPassword);
    }

    return Ok(user);
  }

  private _generateToken(user: Users): {
    accessToken: string;
    refreshToken: string;
  } {
    const jwtConfig = this.configService.getOrThrow<AppConfig['jwt']>('jwt');

    return {
      accessToken: encodeUserJwt({ id: user.id }, jwtConfig.salt, '1d'),
      refreshToken: encodeUserJwt({ id: user.id }, jwtConfig.salt, '7d'),
    };
  }
}
