import { Users } from '@core/db/entities/users';
import { DxError } from '@core/global/dx/dx.constant';

import { AuthError } from './auth.constant';

//  ===============  Data ================
export interface SignInUserData extends Pick<Users, 'email' | 'password'> {}
export interface OtpUserData {
  email: string;
  password: string;
  otp: string;
}

export interface RefreshTokenUserData {
  refreshToken: string;
}

//  ===============  Detail ================
export interface AuthDetails {
  accessToken?: string;
  refreshToken?: string;
  lastSignedInAt: Date;
}

export interface OtpDetails {
  accessToken: string;
  refreshToken: string;
}

export interface RefreshtokenDetails {
  accessToken: string;
}
//  ===============  Error ================

export type PostAuthSignInError =
  | typeof AuthError.InvalidEmailOrPassword
  | typeof AuthError.InActive
  | typeof AuthError.SendOtpFail
  | typeof DxError.DxError
  | typeof AuthError.NotInvited;

export type PostOtpError =
  | typeof AuthError.InvalidEmailOrPassword
  | typeof AuthError.InvalidOtp
  | typeof AuthError.SendOtpFail
  | typeof DxError.DxError;

export type PostRefreshTokenError = typeof AuthError.InvalidToken;
