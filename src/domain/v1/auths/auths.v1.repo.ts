import { Injectable } from '@nestjs/common';

import { Users } from '@core/db/entities/users';
import { BaseRepo } from '@core/shared/common/common.repo';

@Injectable()
export class AuthsV1Repo extends BaseRepo {
  async getOneUser(email: string): Promise<Users | null> {
    return this.from(Users).findOne({
      where: { email },
      relations: { userRoles: { role: true } },
    });
  }

  async getUserByRefreshtoken(refreshToken: string): Promise<Users | null> {
    return this.from(Users).findOneBy({ refreshToken });
  }

  async updateUser(user: Users) {
    const { id, ...data } = user;
    const { userRoles, ...UserData } = data;
    await this.from(Users).update(id, UserData);
  }
}
