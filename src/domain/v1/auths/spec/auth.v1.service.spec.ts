import { ConfigService } from '@nestjs/config';
import { Dayjs } from 'dayjs';
import { mock } from 'jest-mock-extended';
import { hotp } from 'otplib';

import { Users } from '@core/db/entities/users';
import { DxService } from '@core/global/dx/dx.service';
import { emailTemplate } from '@core/global/email/email.contants';
import { EmailService } from '@core/global/email/email.service';
import {
  encodeUserJwt,
  isMatchedHash,
} from '@core/shared/common/common.crypto';
import tzDayjs from '@core/shared/common/common.dayjs';
import { Ok, errIs } from '@core/shared/common/common.neverthrow';
import {
  createTestingModule,
  freezeTestTime,
} from '@core/test/test-util/test-util.common';

import { RoleName } from '@domain/v1/roles/roles.v1.constant';
import { UserStatus } from '@domain/v1/users/users.v1.constant';

import { AuthError } from '../auth.constant';
import { AuthsV1Module } from '../auths.v1.module';
import { AuthsV1Repo } from '../auths.v1.repo';
import { AuthsV1Service } from '../auths.v1.service';

jest.mock('@core/shared/common/common.crypto', () => {
  const original = jest.requireActual('@core/shared/common/common.crypto');
  return {
    ...original,
    isMatchedHash: jest.fn(),
    encodeUserJwt: jest.fn(),
  };
});

jest.mock('otplib', () => ({
  hotp: {
    check: jest.fn(),
    generate: jest.fn(),
  },
  authenticator: {
    generateSecret: jest.fn(),
  },
}));

describe('AuthsV1Service', () => {
  const repo = mock<AuthsV1Repo>();
  const config = mock<ConfigService>();
  const dx = mock<DxService>();
  const email = mock<EmailService>();

  let service: AuthsV1Service;
  let current: Dayjs;

  beforeAll(async () => {
    const module = await createTestingModule(AuthsV1Module)
      .overrideProvider(AuthsV1Repo)
      .useValue(repo)
      .overrideProvider(ConfigService)
      .useValue(config)
      .overrideProvider(DxService)
      .useValue(dx)
      .overrideProvider(EmailService)
      .useValue(email)
      .compile();

    service = module.get(AuthsV1Service);
  });

  describe('postAuthsSignIns', () => {
    current = tzDayjs();
    freezeTestTime(current);

    it('fails when user is not found', async () => {
      const result = await service.postAuthsSignIns({
        email: 'email',
        password: 'password',
      });

      expect(errIs(result._unsafeUnwrapErr(), AuthError.NotInvited)).toEqual(
        true,
      );
    });

    it('fails when user is not active', async () => {
      repo.getOneUser.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        password: 'test',
        status: UserStatus.Invited,
        userRoles: [
          {
            role: { name: RoleName.SuperAdmin },
          },
        ],
      } as Users);

      const result = await service.postAuthsSignIns({
        email: 'email',
        password: 'password',
      });

      expect(errIs(result._unsafeUnwrapErr(), AuthError.InActive)).toEqual(
        true,
      );
    });

    describe('when user is superadmin', () => {
      const superadmin: Users = {
        id: 1,
        email: '<EMAIL>',
        password: 'test',
        status: UserStatus.Active,
        userRoles: [
          {
            role: { name: RoleName.SuperAdmin },
          },
        ],
      } as Users;

      it('works and return token', async () => {
        repo.getOneUser.mockResolvedValueOnce(superadmin);
        repo.updateUser.mockResolvedValueOnce(undefined);

        config.getOrThrow.mockReturnValue('secret');

        (isMatchedHash as jest.Mock).mockReturnValue(Ok(true));
        (encodeUserJwt as jest.Mock).mockReturnValue('token');

        const result = await service.postAuthsSignIns({
          email: 'email',
          password: 'password',
        });

        expect(isMatchedHash).toHaveBeenCalledWith(
          'password',
          superadmin.password,
        );
        expect(repo.updateUser).toHaveBeenCalledWith({
          ...superadmin,
          lastSignedInAt: current.toDate(),
          updatedAt: current.toDate(),
        });
        expect(result.isOk()).toBe(true);
        expect((result as any).value).toEqual({
          accessToken: 'token',
          refreshToken: 'token',
          lastSignedInAt: current.toDate(),
        });
      });

      it('fails when password is incorrect', async () => {
        repo.getOneUser.mockResolvedValueOnce(superadmin);
        repo.updateUser.mockResolvedValueOnce(undefined);

        config.getOrThrow.mockReturnValue('secret');

        (isMatchedHash as jest.Mock).mockReturnValue(false);

        const result = await service.postAuthsSignIns({
          email: 'email',
          password: 'password',
        });

        expect(result.isErr()).toBe(true);
        expect(
          errIs(result._unsafeUnwrapErr(), AuthError.InvalidEmailOrPassword),
        ).toEqual(true);
      });
    });

    describe('when user is not superadmin', () => {
      const user: Users = {
        id: 1,
        email: '<EMAIL>',
        status: UserStatus.Active,
        twoFaSecret: 'test-secret',
        twoFaCounter: 0,
        userRoles: [
          {
            role: { name: RoleName.AdminProvincial },
          },
        ],
      } as Users;

      it('works and send email', async () => {
        repo.getOneUser.mockResolvedValueOnce(user);
        repo.updateUser.mockResolvedValueOnce(undefined);
        config.getOrThrow.mockReturnValue('secret');
        dx.login.mockResolvedValueOnce(
          Ok({
            result: { result: 'ok' },
            userInfo: {},
          } as any),
        );
        email.send.mockResolvedValueOnce(Ok({} as any));

        const result = await service.postAuthsSignIns({
          email: '<EMAIL>',
          password: 'password',
        });

        expect(dx.login).toHaveBeenCalledWith('<EMAIL>', 'password');
        expect(email.send).toHaveBeenCalledWith({
          templateType: emailTemplate.OTP,
          recipient: {
            email: user.email,
            firstname: user.firstname,
            lastname: user.lastname,
            token: hotp.generate(user.twoFaSecret, 1),
          },
          subject: 'ส่ง OTP',
        });
        expect(repo.updateUser).toHaveBeenCalledWith({
          ...user,
          lastSignedInAt: current.toDate(),
          twoFaCounter: 1,
          twoFaSentAt: current.toDate(),
        });
        expect(result.isOk()).toBe(true);
      });

      it('fails when password is incorrect', async () => {
        repo.getOneUser.mockResolvedValueOnce(user);
        repo.updateUser.mockResolvedValueOnce(undefined);

        config.getOrThrow.mockReturnValue('secret');

        dx.login.mockResolvedValueOnce(
          Ok({
            result: { result: 'error' },
            userInfo: {},
          } as any),
        );

        const result = await service.postAuthsSignIns({
          email: 'email',
          password: 'password',
        });

        expect(result.isErr()).toBe(true);
        expect(
          errIs(result._unsafeUnwrapErr(), AuthError.InvalidEmailOrPassword),
        ).toEqual(true);
      });
    });
  });

  describe('postOtp', () => {
    current = tzDayjs();
    freezeTestTime(current);

    const user: Users = {
      id: 1,
      email: '<EMAIL>',
      status: UserStatus.Active,
      twoFaSecret: 'test-secret',
      twoFaCounter: 0,
      twoFaSentAt: current.toDate(),
      userRoles: [
        {
          role: { name: RoleName.AdminProvincial },
        },
      ],
    } as Users;

    it('fails when user is not found', async () => {
      repo.getOneUser.mockResolvedValueOnce(null);

      const result = await service.postOtp({
        email: 'email',
        password: 'password',
        otp: '123456',
      });

      expect(errIs(result._unsafeUnwrapErr(), AuthError.NotInvited)).toEqual(
        true,
      );
    });

    it('fails when user is not active', async () => {
      repo.getOneUser.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        password: 'test',
        status: UserStatus.Invited,
        userRoles: [
          {
            role: { name: RoleName.SuperAdmin },
          },
        ],
      } as Users);

      const result = await service.postOtp({
        email: 'email',
        password: 'password',
        otp: '123456',
      });

      expect(errIs(result._unsafeUnwrapErr(), AuthError.InActive)).toEqual(
        true,
      );
    });

    it('work', async () => {
      repo.getOneUser.mockResolvedValueOnce(user);
      repo.updateUser.mockResolvedValueOnce(undefined);

      dx.login.mockResolvedValueOnce(
        Ok({
          result: { result: 'ok' },
          userInfo: {},
        } as any),
      );

      (hotp.check as jest.Mock).mockReturnValue(true);
      (encodeUserJwt as jest.Mock).mockReturnValue('token');

      config.getOrThrow.mockReturnValue('secret');

      const result = await service.postOtp({
        email: '<EMAIL>',
        password: 'password',
        otp: '123456',
      });

      expect(dx.login).toHaveBeenCalledWith('<EMAIL>', 'password');
      expect(repo.updateUser).toHaveBeenCalledWith({
        ...user,
        refreshToken: 'token',
      });
      expect(result.isOk()).toBe(true);
      expect((result as any).value).toEqual({
        accessToken: 'token',
        refreshToken: 'token',
      });
    });

    it('fails when password is incorrect', async () => {
      repo.getOneUser.mockResolvedValueOnce(user);
      repo.updateUser.mockResolvedValueOnce(undefined);

      config.getOrThrow.mockReturnValue('secret');

      dx.login.mockResolvedValueOnce(
        Ok({
          result: { result: 'error' },
          userInfo: {},
        } as any),
      );

      const result = await service.postOtp({
        email: 'email',
        password: 'password',
        otp: '123456',
      });

      expect(result.isErr()).toBe(true);
      expect(
        errIs(result._unsafeUnwrapErr(), AuthError.InvalidEmailOrPassword),
      ).toEqual(true);
    });
  });

  describe('postRegreshToken', () => {
    it('fails when user is not found', async () => {
      repo.getUserByRefreshtoken.mockResolvedValueOnce(null);

      const result = await service.postRegreshToken({
        refreshToken: 'token',
      });

      expect(repo.getUserByRefreshtoken).toHaveBeenCalledWith('token');
      expect(errIs(result._unsafeUnwrapErr(), AuthError.InvalidToken)).toEqual(
        true,
      );
    });

    it('works', async () => {
      repo.getUserByRefreshtoken.mockResolvedValueOnce({
        id: 1,
        email: '<EMAIL>',
        status: UserStatus.Active,
        userRoles: [
          {
            role: { name: RoleName.AdminProvincial },
          },
        ],
      } as Users);
      (encodeUserJwt as jest.Mock).mockReturnValue('token');

      const result = await service.postRegreshToken({
        refreshToken: 'token',
      });

      expect(repo.getUserByRefreshtoken).toHaveBeenCalledWith('token');
      expect(result.isOk()).toBe(true);
      expect((result as any).value).toEqual({ accessToken: 'token' });
    });
  });
});
