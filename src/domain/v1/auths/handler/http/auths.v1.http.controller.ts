import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { UsePublic } from '@core/middleware/jwt/jwt.common';
import { errIs } from '@core/shared/common/common.neverthrow';
import { ApiException } from '@core/shared/http/http.exception';

import { AuthError } from '../../auth.constant';
import { AuthsV1Service } from '../../auths.v1.service';
import {
  PostAuthsSignInsV1HttpDto,
  PostAuthsSignInsV1HttpResponse,
} from './dto/post-auths-sign-ins.v1.http.dto';
import {
  PostOtpV1HttpDto,
  PostOtpV1HttpResponse,
} from './dto/post-otp.v1.http.dto';
import {
  PostRefreshTokenV1HttpDto,
  PostRefreshTokenV1HttpResponse,
} from './dto/refresh-token.v1.http.dto';

@ApiTags('AuthControllerV1')
@UsePublic()
@Controller({
  path: 'auths',
  version: '1',
})
export class AuthsV1HttpController {
  constructor(private service: AuthsV1Service) {}

  @Post('sign-ins')
  async postAuthsSignIns(
    @Body() body: PostAuthsSignInsV1HttpDto,
  ): Promise<PostAuthsSignInsV1HttpResponse> {
    const r = await this.service.postAuthsSignIns(body);

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data,
      }),
      (e) => {
        if (errIs(e, AuthError.InvalidEmailOrPassword))
          throw new ApiException(e, 400);
        if (errIs(e, AuthError.InActive)) throw new ApiException(e, 400);
        if (errIs(e, AuthError.NotInvited)) throw new ApiException(e, 400);
        throw new ApiException(e, 500);
      },
    );
  }

  @Post('otp')
  async postOtp(
    @Body() body: PostOtpV1HttpDto,
  ): Promise<PostOtpV1HttpResponse> {
    const r = await this.service.postOtp(body);

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data,
      }),
      (e) => {
        if (errIs(e, AuthError.InvalidEmailOrPassword))
          throw new ApiException(e, 400);
        if (errIs(e, AuthError.InActive)) throw new ApiException(e, 400);
        if (errIs(e, AuthError.InvalidOtp)) throw new ApiException(e, 400);
        throw new ApiException(e, 500);
      },
    );
  }

  @Post('refresh-token')
  async refreshToken(
    @Body() body: PostRefreshTokenV1HttpDto,
  ): Promise<PostRefreshTokenV1HttpResponse> {
    const r = await this.service.postRegreshToken(body);

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data,
      }),
      (e) => {
        if (errIs(e, AuthError.InvalidToken)) throw new ApiException(e, 400);
        throw new ApiException(e, 500);
      },
    );
  }
}
