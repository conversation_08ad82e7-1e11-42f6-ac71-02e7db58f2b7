import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString } from 'class-validator';

import { IStandardSingleApiResponse } from '@core/shared/http/http.standard';

import { AuthDetails, SignInUserData } from '@domain/v1/auths/auths.v1.type';

// ====== body =======

export class PostAuthsSignInsV1HttpDto implements SignInUserData {
  @IsEmail()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: '<EMAIL>' })
  email: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'password' })
  password: string;
}

// ===== response =====

class PostAuthsSignInsV1HttpData implements AuthDetails {
  accessToken?: string;
  refreshToken?: string;
  lastSignedInAt: Date;
}

export class PostAuthsSignInsV1HttpResponse
  implements IStandardSingleApiResponse
{
  success: boolean;
  key: string;
  data: PostAuthsSignInsV1HttpData;
}
