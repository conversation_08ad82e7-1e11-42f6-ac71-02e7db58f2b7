import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString } from 'class-validator';

import { IStandardSingleApiResponse } from '@core/shared/http/http.standard';

import { OtpDetails, SignInUserData } from '@domain/v1/auths/auths.v1.type';

// ====== body =======

export class PostOtpV1HttpDto implements SignInUserData {
  @IsEmail()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: '<EMAIL>' })
  email: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'password' })
  password: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: '' })
  otp: string;
}

// ===== response =====

class PostOtpV1HttpData implements OtpDetails {
  accessToken: string;
  refreshToken: string;
}

export class PostOtpV1HttpResponse implements IStandardSingleApiResponse {
  success: boolean;
  key: string;
  data: PostOtpV1HttpData;
}
