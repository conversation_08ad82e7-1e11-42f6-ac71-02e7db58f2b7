import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

import { IStandardSingleApiResponse } from '@core/shared/http/http.standard';

import { RefreshtokenDetails } from '@domain/v1/auths/auths.v1.type';

// ====== body =======

export class PostRefreshTokenV1HttpDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'token' })
  refreshToken: string;
}

// ===== response =====

class PostRefreshTokenV1HttpData implements RefreshtokenDetails {
  accessToken: string;
}

export class PostRefreshTokenV1HttpResponse
  implements IStandardSingleApiResponse
{
  success: boolean;
  key: string;
  data: PostRefreshTokenV1HttpData;
}
