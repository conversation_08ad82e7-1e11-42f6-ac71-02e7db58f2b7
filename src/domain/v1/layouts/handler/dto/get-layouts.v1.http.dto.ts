import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';

import {
  TypeOrmOrderBy,
  TypeOrmOrderByType,
} from '@core/shared/common/common.typeorm';
import { IStandardArrayApiResponse } from '@core/shared/http/http.standard';

import { LayoutDetails } from '../../layouts.v1.type';
import { LayoutDirection } from '../../laytous.v1.constant';

export class GetLayoutsV1HttpParam {
  @ApiPropertyOptional({
    enum: LayoutDirection,
  })
  @IsOptional()
  @IsEnum(LayoutDirection)
  direction?: string;

  @IsOptional()
  @IsEnum(['id', 'createdAt', `updatedAt`])
  sortBy?: string;

  @ApiPropertyOptional({
    enum: TypeOrmOrderBy,
    default: TypeOrmOrderBy.ASC,
    example: TypeOrmOrderBy.ASC,
  })
  @IsOptional()
  @IsEnum(TypeOrmOrderBy)
  orderBy?: TypeOrmOrderByType;
}

class GetLayoutsV1HttpData implements LayoutDetails {
  id: number;
  direction: string;
  layoutImgUrl: string;
  style: any;
  createdAt: Date;
  updatedAt: Date;
}

export class GetLayoutsV1HttpResponse implements IStandardArrayApiResponse {
  success: boolean;
  key: string;
  data: GetLayoutsV1HttpData[];
}
