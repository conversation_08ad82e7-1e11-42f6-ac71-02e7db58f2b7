import { Injectable } from '@nestjs/common';

import { DocumentLayouts } from '@core/db/entities/document-layouts';
import { BaseRepo } from '@core/shared/common/common.repo';
import { TypeOrmOrderByType } from '@core/shared/common/common.typeorm';

@Injectable()
export class LayoutsV1Repo extends BaseRepo {
  async getLayouts(query: {
    direction?: string;
    sortBy?: string;
    orderBy?: TypeOrmOrderByType;
  }): Promise<any> {
    const qb = this.from(DocumentLayouts).createQueryBuilder('layout');

    if (query.direction) {
      qb.andWhere('layout.direction = :direction', {
        direction: query.direction,
      });
    }

    if (query.sortBy) {
      qb.orderBy(`layout.${query.sortBy}`, query.orderBy);
    }

    const datas = await qb.getMany();

    return {
      datas: datas.map((layout) => ({
        id: layout.id,
        filePath: layout.filepath,
        style: layout.style,
        direction: layout.direction,
        createdAt: layout.createdAt,
        updatedAt: layout.updatedAt,
      })),
    };
  }
}
