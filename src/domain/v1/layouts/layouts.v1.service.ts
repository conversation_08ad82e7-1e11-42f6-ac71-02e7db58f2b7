import { Injectable } from '@nestjs/common';

import { MinioService } from '@core/global/minio/minio.service';
import { Ok, Res } from '@core/shared/common/common.neverthrow';
import { TypeOrmOrderBy } from '@core/shared/common/common.typeorm';

import { GetLayoutsV1HttpParam } from './handler/dto/get-layouts.v1.http.dto';
import { LayoutsV1Repo } from './layouts.v1.repo';
import { GetLayouts } from './layouts.v1.type';

@Injectable()
export class LayoutsV1Service {
  constructor(
    private layoutRepo: LayoutsV1Repo,
    private minioService: MinioService,
  ) {}

  async getLayouts(
    options: GetLayoutsV1HttpParam,
  ): Promise<Res<GetLayouts, ''>> {
    const query = {
      direction: options.direction,
      sortBy: options.sortBy || 'id',
      orderBy: options.orderBy || TypeOrmOrderBy.ASC,
    };

    const { datas } = await this.layoutRepo.getLayouts(query);

    const transformedData = await Promise.all(
      datas.map(async (data) => ({
        id: data.id,
        style: data.style || null,
        direction: data.direction,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
        layoutImgUrl: await this.minioService
          .getPresignedUrl({
            object: data.filePath,
          })
          .catch(() => ''),
      })),
    );

    return Ok({
      datas: transformedData,
    });
  }
}
