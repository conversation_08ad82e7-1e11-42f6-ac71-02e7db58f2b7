export const DisasterName = {
  Accident: 'accident',
  Temperature: 'temperature',
  Weather: 'weather',
  DamWaterLevel: 'damWaterLevel',
  Hotspot: 'hotspot',
  AirQuality: 'airQuality',
  Rainfall: 'rainfall',
  SeaWaterLevel: 'seaWaterLevel',
  RiverWaterLevel: 'riverWaterLevel',
};

export const DisasterToTH: Record<keyof typeof DisasterName, string> = {
  Accident: 'อุบัติเหตุ',
  Temperature: 'อุณหภูมิ',
  Weather: 'สภาพอากาศ',
  DamWaterLevel: 'ระดับน้ำในเขื่อน',
  Hotspot: 'จุดความร้อน',
  AirQuality: 'คุณภาพอากาศ',
  Rainfall: 'ปริมาณน้ำฝน',
  SeaWaterLevel: 'ระดับน้ำทะเล',
  RiverWaterLevel: 'ระดับน้ำท่า',
};
