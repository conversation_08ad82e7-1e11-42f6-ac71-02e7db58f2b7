import { Injectable } from '@nestjs/common';
import {
  FindManyOptions,
  FindOptionsRelations,
  FindOptionsWhere,
} from 'typeorm';

import { Departments } from '@core/db/entities/departments';
import { BaseRepo } from '@core/shared/common/common.repo';

@Injectable()
export class DepartmentsV1Repo extends BaseRepo {
  async getOneDepartment(query: {
    where?: FindOptionsWhere<Departments>;
    relations?: FindOptionsRelations<Departments>;
  }): Promise<Departments | null> {
    return this.from(Departments).findOne({
      where: query.where,
      relations: query.relations,
    });
  }

  async getAllDepartments(options?: FindManyOptions): Promise<Departments[]> {
    return this.from(Departments).find(options);
  }
}
