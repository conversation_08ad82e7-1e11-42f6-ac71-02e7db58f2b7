import { Injectable } from '@nestjs/common';

import { Err, Ok, Res } from '@core/shared/common/common.neverthrow';

import { DepartmentsV1Repo } from './departments.v1.repo';
import { DepartmentDetails } from './departments.v1.type';

@Injectable()
export class DepartmentsV1Service {
  constructor(private readonly repo: DepartmentsV1Repo) {}
  async getOneDepartmentDetail(
    id: number,
  ): Promise<Res<DepartmentDetails, 'notFound'>> {
    const department = await this.repo.getOneDepartment({ where: { id } });

    if (!department) {
      return Err('notFound');
    }

    return Ok({
      id: department.id,
      nameTh: department.nameTh,
      nameEn: department.nameEn,
      code: department.code,
    });
  }
}
