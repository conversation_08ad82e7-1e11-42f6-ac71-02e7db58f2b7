import { Module } from '@nestjs/common';

import { DepartmentsV1Repo } from './departments.v1.repo';
import { DepartmentsV1Service } from './departments.v1.service';
import { DepartmentsV1HttpController } from './handler/http/departments.v1.controller';

@Module({
  providers: [DepartmentsV1Service, DepartmentsV1Repo],
  controllers: [DepartmentsV1HttpController],
  exports: [DepartmentsV1Repo],
})
export class DepartmentsV1Module {}
