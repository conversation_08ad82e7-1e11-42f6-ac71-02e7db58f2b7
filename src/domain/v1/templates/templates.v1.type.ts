import { IPaginationSchema } from '@core/shared/http/http.standard';

export interface GetPaginateTemplates {
  datas: TemplatePaginateDetails[];
  pagination: IPaginationSchema;
}

export interface TemplatePaginateDetails {
  id: number;
  name: string;
  status: string;
  actionType: string;
  thumbnailUrl: string;
  backgroundColor: string;
  department: {
    id: number;
    nameTh: string;
  };
  permission: {
    canDelete: boolean;
  };
}

export interface GetTemplateDetail {
  datas: TemplateDetails;
}

export interface TemplateDetails {
  id: number;
  name: string;
  status: string;
  actionType: string;
  documentType: string;
  data: any;
  backgroundColor: string;
  thumbnailUrl: string;
  layout: {
    id: number;
    direction: string;
  };
  department: {
    id: number;
    nameTh: string;
  };
  disasters?: DisasterTemplateDetails[];
  createdBy: {
    id: number;
    firstname: string;
    lastname: string;
    isMadeByMe: boolean;
  };
  reviewedBy?: {
    id?: number;
    firstname?: string;
    lastname?: string;
    isMadeByMe?: boolean;
  };
  latestRequestedBy?: {
    id?: number;
    firstname?: string;
    lastname?: string;
    isMadeByMe?: boolean;
  };
  latestEditedBy?: {
    id?: number;
    firstname?: string;
    lastname?: string;
    isMadeByMe?: boolean;
  };
  createdAt: Date;
  reviewedAt?: Date | null;
  latestRequestedAt?: Date | null;
  latestEditedAt?: Date | null;

  permission: {
    canCreate: boolean;
    canEdit: boolean;
  };
}

export interface DisasterTemplateDetails {
  id: number;
  name: string;
}
