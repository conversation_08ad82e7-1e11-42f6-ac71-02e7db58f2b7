import { <PERSON>, Get, Param, ParseIntPipe, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { Users } from '@core/db/entities/users';
import { ReqUser } from '@core/middleware/jwt/jwt.common';
import { ApiException } from '@core/shared/http/http.exception';

import { TemplatesV1Service } from '../../templates.v1.service';
import { GetTemplateDetailsV1HttpResponse } from './get-template-details.v1.dto';
import {
  GetTemplatesV1HttpParam,
  GetTemplatesV1HttpResponse,
} from './get-templates.v1.http.dto';

@ApiTags('TemplateControllerV1')
@ApiBearerAuth()
@Controller({
  path: 'templates',
  version: '1',
})
export class TemplatesV1HttpController {
  constructor(private templateService: TemplatesV1Service) {}

  @Get()
  async getTemplates(
    @Query() options: GetTemplatesV1HttpParam,
    @ReqUser() user: Users,
  ): Promise<GetTemplatesV1HttpResponse> {
    const r = await this.templateService.getPaginateTemplates(user, options);

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data: data.datas,
        meta: {
          pagination: data.pagination,
        },
      }),
      (e) => {
        throw new ApiException(e, 500);
      },
    );
  }

  @Get(':id')
  async getTemplateDetail(
    @Param('id', ParseIntPipe) id: number,
    @ReqUser() user: Users,
  ): Promise<GetTemplateDetailsV1HttpResponse> {
    const r = await this.templateService.getTemplateDetailById(id, user);

    return r.match(
      (data) => ({
        success: true,
        key: '',
        data: data.datas,
      }),
      (e) => {
        throw new ApiException(e, 500);
      },
    );
  }
}
