import { IStandardSingleApiResponse } from '@core/shared/http/http.standard';

import {
  DisasterTemplateDetails,
  TemplateDetails,
} from '../../templates.v1.type';

class GetTemplateDetailsV1HttpData implements TemplateDetails {
  id: number;
  name: string;
  status: string;
  actionType: string;
  documentType: string;
  data: any;
  backgroundColor: string;
  thumbnailUrl: string;
  layout: { id: number; direction: string };
  department: { id: number; nameTh: string };
  disasters?: DisasterTemplateDetails[];

  createdBy: {
    id: number;
    firstname: string;
    lastname: string;
    isMadeByMe: boolean;
  };
  reviewedBy?: {
    id?: number;
    firstname?: string;
    lastname?: string;
    isMadeByMe?: boolean;
  };

  latestRequestedBy?: {
    id?: number;
    firstname?: string;
    lastname?: string;
    isMadeByMe?: boolean;
  };

  latestEditedBy?: {
    id?: number;
    firstname?: string;
    lastname?: string;
    isMadeByMe?: boolean;
  };

  createdAt: Date;
  reviewedAt?: Date | null;
  latestRequestedAt?: Date | null;
  latestEditedAt?: Date | null;

  permission: { canCreate: boolean; canEdit: boolean };
}

export class GetTemplateDetailsV1HttpResponse
  implements IStandardSingleApiResponse
{
  success: boolean;
  key: string;
  data: GetTemplateDetailsV1HttpData;
}
