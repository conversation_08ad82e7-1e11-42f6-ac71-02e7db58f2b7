import { Documents } from '@core/db/entities/documents';
import tzDayjs from '@core/shared/common/common.dayjs';
import {
  PaginationOptions,
  getLimit,
  getOffset,
  getPagination,
} from '@core/shared/common/common.pagintaion';
import { BaseRepo } from '@core/shared/common/common.repo';
import { TypeOrmOrderByType } from '@core/shared/common/common.typeorm';

import {
  ApproveStatus,
  DocumentType,
} from '../documents/documents.v1.constant';

export class TemplatesV1Repo extends BaseRepo {
  async getPaginateTemplates(
    paginatedOptions: PaginationOptions,
    query: {
      search?: string;
      startDate?: Date;
      endDate?: Date;
      departmentIds?: number[];
      disasterIds?: number[];
      layoutDirections?: string[];
      orderBy: TypeOrmOrderByType;
      sortBy: string;
    },
  ) {
    const qb = this.from(Documents)
      .createQueryBuilder('doc')
      .select([
        'doc.id',
        'doc.name',
        'doc.status',
        'doc.actionType',
        'doc.backgroundColor',
        'doc.reviewedAt',
        'doc.createdAt',
        'doc.latestEditedAt',
        'layout.id',
        'disaster.id',
        'thumbnail.resolutionLowPath',
        'createdBy.id',
        'department.id',
        'department.nameTh',
      ])
      .leftJoin('doc.layout', 'layout')
      .leftJoin('doc.disasters', 'disaster')
      .leftJoin('doc.thumbnail', 'thumbnail')
      .leftJoin('doc.createdBy', 'createdBy')
      .leftJoin('createdBy.department', 'department')
      .where('doc.documentType = :documentType', {
        documentType: DocumentType.Template,
      })
      .andWhere('doc.status = :status', {
        status: ApproveStatus.Approved,
      });

    if (query.search) {
      qb.andWhere('doc.name ILIKE :search', {
        search: `%${query.search.trim()}%`,
      });
    }

    if (query.startDate) {
      qb.andWhere('doc.reviewedAt >= :startDate', {
        startDate: tzDayjs(query.startDate).startOf('day').toDate(),
      });
    }

    if (query.endDate) {
      qb.andWhere('doc.reviewedAt <= :endDate', {
        endDate: tzDayjs(query.endDate).endOf('day').toDate(),
      });
    }

    if (query.departmentIds && query.departmentIds.length > 0) {
      qb.andWhere('department.id IN (:...departmentIds)', {
        departmentIds: query.departmentIds,
      });
    }

    if (query.layoutDirections && query.layoutDirections.length > 0) {
      qb.andWhere('layout.direction IN (:...layoutDirections)', {
        layoutDirections: query.layoutDirections,
      });
    }

    if (query.disasterIds && query.disasterIds.length > 0) {
      qb.andWhere('disaster.id IN (:...disasterIds)', {
        disasterIds: query.disasterIds,
      });
    }

    if (query.sortBy === 'name') {
      qb.orderBy('doc.name', query.orderBy);
    } else if (query.sortBy === 'latestEditedAt') {
      qb.orderBy('doc.latestEditedAt', query.orderBy);
    } else if (query.sortBy === 'createdAt') {
      qb.orderBy('doc.createdAt', query.orderBy);
    } else {
      qb.orderBy('doc.reviewedAt', query.orderBy);
    }

    const datas = await qb
      .take(getLimit(paginatedOptions))
      .skip(getOffset(paginatedOptions))
      .getMany();

    const totalItems = await qb.getCount();

    return {
      datas: datas.map((data) => ({
        id: data.id,
        name: data.name,
        status: data.status,
        actionType: data.actionType,
        backgroundColor: data.backgroundColor,
        thumbnail: data.thumbnail,
        createdBy: {
          id: data.createdBy.id,
        },
        department: {
          id: data.createdBy?.department?.id,
          nameTh: data.createdBy?.department?.nameTh,
        },
      })),
      pagination: getPagination(datas, totalItems, paginatedOptions),
    };
  }

  async getTemplateDetailById(id: number) {
    const qb = this.from(Documents)
      .createQueryBuilder('doc')
      .select([
        'doc.id',
        'doc.name',
        'doc.status',
        'doc.actionType',
        'doc.documentType',
        'doc.backgroundColor',
        'doc.data',
        'doc.responsibilityAreaId',
        'layout.id',
        'layout.direction',
        'disaster.id',
        'disaster.name',
        'thumbnail.resolutionHighPath',
        'department.id',
        'department.nameTh',

        'doc.reviewedAt',
        'reviewedBy.id',
        'reviewedBy.firstname',
        'reviewedBy.lastname',

        'doc.latestRequestedAt',
        'latestRequestedBy.id',
        'latestRequestedBy.firstname',
        'latestRequestedBy.lastname',

        'doc.latestEditedAt',
        'latestEditedBy.id',
        'latestEditedBy.firstname',
        'latestEditedBy.lastname',

        'doc.createdAt',
        'createdBy.id',
        'createdBy.firstname',
        'createdBy.lastname',
      ])
      .leftJoin('doc.layout', 'layout')
      .leftJoin('doc.disasters', 'disaster')
      .leftJoin('doc.thumbnail', 'thumbnail')
      .leftJoin('doc.reviewedBy', 'reviewedBy')
      .leftJoin('doc.latestRequestedBy', 'latestRequestedBy')
      .leftJoin('doc.latestEditedBy', 'latestEditedBy')
      .leftJoin('doc.createdBy', 'createdBy')
      .leftJoin('createdBy.department', 'department')
      .where('doc.id = :id', { id })
      .andWhere('doc.documentType = :documentType', {
        documentType: DocumentType.Template,
      });

    const datas = await qb.getOne();

    return datas;
  }
}
