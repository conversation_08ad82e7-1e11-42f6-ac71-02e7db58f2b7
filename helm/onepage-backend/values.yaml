# Default values for onepage-backend.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: ""
  pullPolicy: Always
  tag: ""

imagePullSecrets: []

nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 3000

ingress:
  enabled: false
  className: "nginx"
  annotations: {}
  hosts: []
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  limits:
    cpu: "1"
    memory: 1Gi
  requests:
    cpu: 250m
    memory: 500Mi

# This is to setup the liveness and readiness probes more information can be found here: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/
livenessProbe:
  httpGet:
    path: /api/health
    port: http
  initialDelaySeconds: 30
  timeoutSeconds: 30
  periodSeconds: 15
  failureThreshold: 3
readinessProbe:
  httpGet:
    path: /api/health
    port: http
  initialDelaySeconds: 30
  timeoutSeconds: 30
  periodSeconds: 15
  failureThreshold: 3

dnsConfig: {}

# This section is for setting up autoscaling more information can be found here: https://kubernetes.io/docs/concepts/workloads/autoscaling/
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

env: {}

secretEnv: {}

enabledDbMigration: true
