{{- if .Values.enabledDbMigration }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "onepage-backend.fullname" . }}-db-migration
  labels: {{- include "onepage-backend.labels" . | nindent 4 }}
  annotations:
    helm.sh/hook: pre-install,pre-upgrade,pre-rollback
    helm.sh/hook-weight: "-1"
    helm.sh/hook-delete-policy: before-hook-creation
spec:
  ttlSecondsAfterFinished: 60
  template:
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}-db-migration
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            - /bin/sh
            - -c
            - yarn db:deploy
          {{- if or .Values.env .Values.secretEnv }}
          envFrom:
            {{- if .Values.env }}
            - configMapRef:
                name: {{ include "onepage-backend.fullname" . }}
            {{- end }}
            {{- if .Values.secretEnv }}
            - secretRef:
                name: {{ include "onepage-backend.fullname" . }}
            {{- end }}
          {{- end }}
          {{- with .Values.volumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.volumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.dnsConfig }}
      dnsConfig:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      dnsPolicy: {{ default "ClusterFirst" .Values.dnsPolicy }}
      restartPolicy: Never
{{- end }}